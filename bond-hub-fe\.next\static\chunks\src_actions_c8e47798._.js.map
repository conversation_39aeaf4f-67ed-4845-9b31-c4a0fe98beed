{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAUsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA+BsB,YAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgDsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAsJsB,eAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAwNsB,yBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA6OsB,0BAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA8PsB,gBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgRsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/actions/auth.action.ts"], "sourcesContent": ["\"use server\";\r\nimport axios from \"axios\";\r\nimport axiosInstance, {\r\n  createAxiosInstance,\r\n  refreshTokenAxios,\r\n} from \"@/lib/axios\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { DeviceType } from \"@/types/base\";\r\nimport { isEmail } from \"@/utils/helpers\";\r\n\r\nexport async function initiateRegistration(identifier: string) {\r\n  try {\r\n    // Determine if the identifier is an email or phone number\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/initiate\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { registrationId } = response.data;\r\n\r\n    return { success: true, registrationId };\r\n  } catch (error) {\r\n    console.error(\"Initiate registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyOtp(registrationId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/verify\", {\r\n      registrationId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"OTP verification failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function completeRegistration(\r\n  registrationId: string,\r\n  password: string,\r\n  fullName: string,\r\n  dateOfBirth: string,\r\n  gender: string,\r\n) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/register/complete\", {\r\n      registrationId,\r\n      password,\r\n      fullName,\r\n      dateOfBirth,\r\n      gender,\r\n    });\r\n    const { user } = response.data;\r\n\r\n    return { success: true, user };\r\n  } catch (error) {\r\n    console.error(\"Complete registration failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\nexport async function login(\r\n  identifier: string,\r\n  password: string,\r\n  deviceName: string,\r\n  deviceType: DeviceType,\r\n) {\r\n  try {\r\n    // Create a clean axios instance for login (no token needed)\r\n    const serverAxios = createAxiosInstance();\r\n\r\n    // Determine if identifier is email or phone number\r\n    const response = await serverAxios.post(\"/auth/login\", {\r\n      [isEmail(identifier) ? \"email\" : \"phoneNumber\"]: identifier,\r\n      password,\r\n      deviceName,\r\n      deviceType,\r\n    });\r\n\r\n    // Extract response data\r\n    const { user, accessToken, refreshToken, deviceId } = response.data;\r\n\r\n    // Validate required fields\r\n    if (!accessToken || !refreshToken || !deviceId) {\r\n      throw new Error(\"Invalid login response: missing required tokens\");\r\n    }\r\n\r\n    return { success: true, user, accessToken, refreshToken, deviceId };\r\n  } catch (error) {\r\n    console.error(\"Login failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function logout() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const accessToken = authState.accessToken || \"\";\r\n\r\n    // Only attempt to call the logout API if we have a refresh token\r\n    if (refreshToken) {\r\n      try {\r\n        const serverAxios = createAxiosInstance(accessToken);\r\n        await serverAxios.post(\r\n          \"/auth/logout\",\r\n          {},\r\n          {\r\n            headers: { \"refresh-token\": refreshToken },\r\n          },\r\n        );\r\n      } catch (apiError) {\r\n        // Log but continue with local logout even if API call fails\r\n        console.error(\r\n          \"API logout failed, continuing with local logout:\",\r\n          apiError,\r\n        );\r\n      }\r\n    }\r\n\r\n    // Always return success to ensure UI updates\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Logout failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Làm mới token - This function is now primarily handled by the axios interceptors\r\n// but we keep this for explicit token refresh calls if needed\r\nexport async function refreshToken() {\r\n  try {\r\n    const authState = useAuthStore.getState();\r\n    const refreshToken = authState.refreshToken;\r\n    const deviceId = authState.deviceId;\r\n\r\n    if (!refreshToken) {\r\n      throw new Error(\"No refresh token available\");\r\n    }\r\n\r\n    if (!deviceId) {\r\n      throw new Error(\"No device ID available\");\r\n    }\r\n\r\n    // Use the dedicated refresh token axios instance\r\n    const response = await refreshTokenAxios.post(\"/auth/refresh\", {\r\n      refreshToken,\r\n      deviceId,\r\n    });\r\n\r\n    if (!response.data || !response.data.accessToken) {\r\n      throw new Error(\"Invalid response from refresh token API\");\r\n    }\r\n\r\n    const { accessToken } = response.data;\r\n    const device = response.data.device;\r\n\r\n    // Update tokens in the store if in browser environment\r\n    if (typeof window !== \"undefined\") {\r\n      useAuthStore.getState().setTokens(accessToken, refreshToken);\r\n    }\r\n\r\n    // Update cookie if running on server\r\n    if (typeof window === \"undefined\") {\r\n      try {\r\n        const { cookies } = await import(\"next/headers\");\r\n        (await cookies()).set(\"access_token\", accessToken, {\r\n          httpOnly: true,\r\n          secure: process.env.NODE_ENV === \"production\",\r\n          maxAge: 60 * 60 * 24 * 7, // 7 days\r\n          path: \"/\",\r\n        });\r\n      } catch (cookieError) {\r\n        console.error(\"Failed to set cookie:\", cookieError);\r\n        // Continue even if cookie setting fails\r\n      }\r\n    }\r\n\r\n    return { success: true, accessToken, device };\r\n  } catch (error) {\r\n    // Only logout if we're in the browser\r\n    if (typeof window !== \"undefined\") {\r\n      try {\r\n        await useAuthStore.getState().logout();\r\n      } catch (logoutError) {\r\n        console.error(\"Error during logout:\", logoutError);\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function initiateForgotPassword(identifier: string) {\r\n  try {\r\n    // Kiểm tra xem identifier là email hay số điện thoại\r\n    const isEmailFormat = isEmail(identifier);\r\n\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password\", {\r\n      [isEmailFormat ? \"email\" : \"phoneNumber\"]: identifier,\r\n    });\r\n    const { resetId } = response.data;\r\n\r\n    return { success: true, resetId };\r\n  } catch (error) {\r\n    console.error(\"Initiate forgot password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function verifyForgotPasswordOtp(resetId: string, otp: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/verify\", {\r\n      resetId,\r\n      otp,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Verify forgot password OTP failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function resetPassword(resetId: string, newPassword: string) {\r\n  try {\r\n    const serverAxios = createAxiosInstance();\r\n    const response = await serverAxios.post(\"/auth/forgot-password/reset\", {\r\n      resetId,\r\n      newPassword,\r\n    });\r\n    return { success: true, data: response.data };\r\n  } catch (error) {\r\n    console.error(\"Reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n\r\n// Thay đổi mật khẩu (khi đã đăng nhập)\r\nexport async function changePassword(\r\n  currentPassword: string,\r\n  newPassword: string,\r\n  accessToken: string,\r\n) {\r\n  try {\r\n    if (!accessToken) {\r\n      return {\r\n        success: false,\r\n        error: \"Bạn cần đăng nhập lại để thực hiện thao tác này\",\r\n      };\r\n    }\r\n\r\n    const serverAxios = createAxiosInstance(accessToken);\r\n    const response = await serverAxios.put(\"/auth/change-password\", {\r\n      currentPassword,\r\n      newPassword,\r\n    });\r\n\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Đổi mật khẩu thành công\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Change password failed:\", error);\r\n\r\n    // Xử lý lỗi từ API\r\n    if (axios.isAxiosError(error) && error.response) {\r\n      console.error(\"Error response:\", {\r\n        status: error.response.status,\r\n        data: error.response.data,\r\n      });\r\n\r\n      // Kiểm tra lỗi mật khẩu cũ không đúng\r\n      if (error.response.status === 400 || error.response.status === 401) {\r\n        if (error.response.data?.message) {\r\n          // Nếu server trả về thông báo lỗi cụ thể\r\n          return {\r\n            success: false,\r\n            error: error.response.data.message,\r\n          };\r\n        }\r\n\r\n        // Nếu không có thông báo cụ thể, kiểm tra nếu là lỗi mật khẩu cũ không đúng\r\n        if (error.response.data?.error === \"INVALID_CURRENT_PASSWORD\") {\r\n          return {\r\n            success: false,\r\n            error: \"Mật khẩu hiện tại không đúng\",\r\n          };\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      success: false,\r\n      error:\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Đã xảy ra lỗi khi đổi mật khẩu\",\r\n    };\r\n  }\r\n}\r\n\r\n// Xác nhận đặt lại mật khẩu với token (qua email)\r\nexport async function confirmResetPassword(token: string, newPassword: string) {\r\n  try {\r\n    const response = await axiosInstance.post(\"/auth/reset-password/confirm\", {\r\n      token,\r\n      newPassword,\r\n    });\r\n    return {\r\n      success: true,\r\n      message: response.data.message || \"Password has been reset successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Confirm reset password failed:\", error);\r\n    return {\r\n      success: false,\r\n      error: error instanceof Error ? error.message : \"Unknown error\",\r\n    };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAgVsB,uBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}]}