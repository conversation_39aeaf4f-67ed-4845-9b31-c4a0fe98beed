{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ConverstationList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Ava<PERSON>, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { formatMessageTime } from \"@/utils/dateUtils\";\r\nimport { getUserInitials, getUserDisplayName } from \"@/utils/userUtils\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport SearchHeader from \"../SearchHeader\";\r\nimport { Media } from \"@/types/base\";\r\nimport { RefreshCw } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useEffect } from \"react\";\r\n// Toast removed - handled at UI level only\r\n\r\ninterface ContactListProps {\r\n  onSelectContact: (contactId: string | null) => void;\r\n  onSelectGroup: (groupId: string | null) => void;\r\n}\r\n\r\n// Helper function to format the last message media for display\r\nconst formatLastMessageMedia = (media: Media[]) => {\r\n  if (!media || media.length === 0) return \"\";\r\n\r\n  // Count media types\r\n  const imageCount = media.filter((m) => m.type === \"IMAGE\").length;\r\n  const videoCount = media.filter((m) => m.type === \"VIDEO\").length;\r\n  const audioCount = media.filter((m) => m.type === \"AUDIO\").length;\r\n  const documentCount = media.filter(\r\n    (m) => m.type !== \"IMAGE\" && m.type !== \"VIDEO\" && m.type !== \"AUDIO\",\r\n  ).length;\r\n\r\n  // Format based on media types present\r\n  if (audioCount > 0) {\r\n    if (audioCount === media.length) {\r\n      return audioCount === 1 ? \"[Âm thanh]\" : `[${audioCount} âm thanh]`;\r\n    }\r\n  }\r\n\r\n  if (\r\n    imageCount > 0 &&\r\n    videoCount === 0 &&\r\n    audioCount === 0 &&\r\n    documentCount === 0\r\n  ) {\r\n    return imageCount === 1 ? \"[Hình ảnh]\" : `[${imageCount} hình ảnh]`;\r\n  }\r\n\r\n  if (\r\n    videoCount > 0 &&\r\n    imageCount === 0 &&\r\n    audioCount === 0 &&\r\n    documentCount === 0\r\n  ) {\r\n    return videoCount === 1 ? \"[Video]\" : `[${videoCount} video]`;\r\n  }\r\n\r\n  if (\r\n    documentCount > 0 &&\r\n    imageCount === 0 &&\r\n    videoCount === 0 &&\r\n    audioCount === 0\r\n  ) {\r\n    return documentCount === 1\r\n      ? \"[Tệp đính kèm]\"\r\n      : `[${documentCount} tệp đính kèm]`;\r\n  }\r\n\r\n  // Mixed media types\r\n  return \"[Đa phương tiện]\";\r\n};\r\n\r\nexport default function ContactList({\r\n  onSelectContact,\r\n  onSelectGroup,\r\n}: ContactListProps) {\r\n  const { selectedContact, selectedGroup, currentChatType } = useChatStore();\r\n  const currentUser = useAuthStore((state) => state.user);\r\n  const {\r\n    isLoading,\r\n    // searchQuery, setSearchQuery,\r\n    getFilteredConversations,\r\n    loadConversations,\r\n  } = useConversationsStore();\r\n\r\n  // Function to refresh conversations\r\n  const refreshConversations = () => {\r\n    if (currentUser?.id) {\r\n      loadConversations(currentUser.id)\r\n        .then(() => {\r\n          // Success - no toast in store/action level\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Failed to refresh conversations:\", error);\r\n          // Error - no toast in store/action level\r\n        });\r\n    }\r\n  };\r\n\r\n  // Setup global trigger function for socket events\r\n  useEffect(() => {\r\n    if (typeof window !== \"undefined\") {\r\n      window.triggerConversationsReload = refreshConversations;\r\n    }\r\n\r\n    // Cleanup on unmount\r\n    return () => {\r\n      if (typeof window !== \"undefined\") {\r\n        window.triggerConversationsReload = undefined;\r\n      }\r\n    };\r\n  }, [currentUser?.id]);\r\n\r\n  // Get filtered conversations based on search query\r\n  const filteredConversations = getFilteredConversations();\r\n\r\n  // Log conversations for debugging\r\n  console.log(\r\n    \"Filtered conversations:\",\r\n    filteredConversations.map((conv) => ({\r\n      id: conv.type === \"GROUP\" ? conv.group?.id : conv.contact.id,\r\n      name:\r\n        conv.type === \"GROUP\"\r\n          ? conv.group?.name\r\n          : conv.contact.userInfo?.fullName,\r\n      type: conv.type,\r\n    })),\r\n  );\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full w-full\">\r\n      <div className=\"flex items-center justify-between w-full border-r-0 border-b h-[69px]\">\r\n        <SearchHeader className=\"flex-1 border-none\" />\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"mr-2\"\r\n          onClick={refreshConversations}\r\n          title=\"Làm mới danh sách cuộc trò chuyện\"\r\n        >\r\n          <RefreshCw className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-y-auto custom-scrollbar\">\r\n        {isLoading ? (\r\n          <div className=\"flex justify-center items-center h-20\">\r\n            <p className=\"text-gray-500\">Đang tải danh sách người dùng...</p>\r\n          </div>\r\n        ) : filteredConversations.length > 0 ? (\r\n          filteredConversations.map((conversation, index) => {\r\n            // Determine if this is a user or group conversation\r\n            const isGroupConversation = conversation.type === \"GROUP\";\r\n            const isSelected = isGroupConversation\r\n              ? selectedGroup?.id === conversation.group?.id &&\r\n                currentChatType === \"GROUP\"\r\n              : selectedContact?.id === conversation.contact.id &&\r\n                currentChatType === \"USER\";\r\n\r\n            // Generate a unique key that includes both the ID and index\r\n            const uniqueKey = isGroupConversation\r\n              ? `group-${conversation.group?.id}-${index}`\r\n              : `user-${conversation.contact.id}-${index}`;\r\n\r\n            return (\r\n              <div\r\n                key={uniqueKey}\r\n                className={`flex items-center gap-3 p-3 hover:bg-gray-100 cursor-pointer ${\r\n                  isSelected ? \"bg-blue-50\" : \"\"\r\n                }`}\r\n                onClick={() => {\r\n                  if (isGroupConversation && conversation.group) {\r\n                    onSelectGroup(conversation.group.id);\r\n                  } else {\r\n                    onSelectContact(conversation.contact.id);\r\n                  }\r\n                }}\r\n              >\r\n                <div className=\"relative\">\r\n                  <Avatar className=\"h-12 w-12 border\">\r\n                    <AvatarImage\r\n                      src={\r\n                        isGroupConversation\r\n                          ? conversation.group?.avatarUrl || \"\"\r\n                          : conversation.contact.userInfo?.profilePictureUrl ||\r\n                            \"\"\r\n                      }\r\n                      className=\"object-cover\"\r\n                    />\r\n                    <AvatarFallback>\r\n                      {isGroupConversation\r\n                        ? conversation.group?.name\r\n                            ?.substring(0, 2)\r\n                            .toUpperCase() || \"GP\"\r\n                        : getUserInitials(conversation.contact)}\r\n                    </AvatarFallback>\r\n                  </Avatar>\r\n                  {/* Online status indicator - only for user conversations */}\r\n                  {!isGroupConversation && (\r\n                    <>\r\n                      {conversation.contact.online ? (\r\n                        <span className=\"absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white\"></span>\r\n                      ) : conversation.contact.userInfo?.lastSeen ? (\r\n                        <span className=\"absolute bottom-0 right-0 h-3 w-3 rounded-full bg-gray-300 border-2 border-white\"></span>\r\n                      ) : null}\r\n                    </>\r\n                  )}\r\n                </div>\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <div className=\"flex justify-between items-center\">\r\n                    <div className=\"flex items-center\">\r\n                      <p className=\"font-medium truncate\">\r\n                        {isGroupConversation\r\n                          ? conversation.group?.name\r\n                          : getUserDisplayName(conversation.contact)}\r\n                      </p>\r\n                    </div>\r\n                    <div className=\"flex items-center\">\r\n                      {conversation.lastMessage && (\r\n                        <span className=\"text-xs text-gray-500 whitespace-nowrap ml-1\">\r\n                          {formatMessageTime(\r\n                            conversation.lastMessage.createdAt,\r\n                          )}\r\n                        </span>\r\n                      )}\r\n                      {conversation.unreadCount > 0 && (\r\n                        <span className=\"bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center ml-1\">\r\n                          {conversation.unreadCount}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                  {conversation.isTyping ? (\r\n                    <p className=\"text-sm text-blue-500 truncate flex items-center\">\r\n                      {isGroupConversation &&\r\n                      conversation.typingUsers &&\r\n                      conversation.typingUsers.length > 0 ? (\r\n                        <>\r\n                          {conversation.typingUsers[0].fullName} đang nhập\r\n                          {conversation.typingUsers.length > 1 &&\r\n                            ` và ${conversation.typingUsers.length - 1} người khác`}\r\n                        </>\r\n                      ) : (\r\n                        \"Đang nhập\"\r\n                      )}\r\n                      <span className=\"ml-1 flex\">\r\n                        <span className=\"animate-bounce mx-0.5 delay-0\">.</span>\r\n                        <span className=\"animate-bounce mx-0.5 delay-100\">\r\n                          .\r\n                        </span>\r\n                        <span className=\"animate-bounce mx-0.5 delay-200\">\r\n                          .\r\n                        </span>\r\n                      </span>\r\n                    </p>\r\n                  ) : conversation.lastMessage ? (\r\n                    <p className=\"text-sm text-gray-500 truncate\">\r\n                      {conversation.lastMessage.recalled\r\n                        ? \"Tin nhắn đã được thu hồi\"\r\n                        : // Add prefix based on sender\r\n                          (isGroupConversation &&\r\n                          conversation.lastMessage.senderId !== currentUser?.id\r\n                            ? (() => {\r\n                                // Try to get sender name from multiple sources\r\n                                const lastMessage = conversation.lastMessage!;\r\n                                const senderName =\r\n                                  lastMessage.sender?.userInfo?.fullName ||\r\n                                  // Try to find sender in group members (API format)\r\n                                  conversation.group?.memberUsers?.find(\r\n                                    (m) => m.id === lastMessage.senderId,\r\n                                  )?.fullName ||\r\n                                  // Try to find sender in group members (store format)\r\n                                  conversation.group?.members?.find(\r\n                                    (m) => m.userId === lastMessage.senderId,\r\n                                  )?.user?.userInfo?.fullName ||\r\n                                  \"Người dùng\";\r\n                                return senderName + \": \";\r\n                              })()\r\n                            : conversation.lastMessage.senderId ===\r\n                                currentUser?.id\r\n                              ? \"Bạn: \"\r\n                              : \"\") +\r\n                          (conversation.lastMessage.content?.text ||\r\n                            (conversation.lastMessage.content?.media?.length\r\n                              ? formatLastMessageMedia(\r\n                                  conversation.lastMessage.content.media,\r\n                                )\r\n                              : \"\"))}\r\n                    </p>\r\n                  ) : isGroupConversation ? (\r\n                    <p className=\"text-sm text-gray-500 truncate\">Nhóm chat</p>\r\n                  ) : conversation.contact.userInfo?.statusMessage ? (\r\n                    <p className=\"text-sm text-gray-500 truncate\">\r\n                      {conversation.contact.userInfo.statusMessage}\r\n                    </p>\r\n                  ) : null}\r\n                </div>\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className=\"flex justify-center items-center h-20\">\r\n            <p className=\"text-gray-500\">Không tìm thấy người dùng nào</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAZA;;;;;;;;;;;AAoBA,+DAA+D;AAC/D,MAAM,yBAAyB,CAAC;IAC9B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG,OAAO;IAEzC,oBAAoB;IACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,MAAM;IACjE,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,MAAM;IACjE,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,SAAS,MAAM;IACjE,MAAM,gBAAgB,MAAM,MAAM,CAChC,CAAC,IAAM,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,SAC9D,MAAM;IAER,sCAAsC;IACtC,IAAI,aAAa,GAAG;QAClB,IAAI,eAAe,MAAM,MAAM,EAAE;YAC/B,OAAO,eAAe,IAAI,eAAe,CAAC,CAAC,EAAE,WAAW,UAAU,CAAC;QACrE;IACF;IAEA,IACE,aAAa,KACb,eAAe,KACf,eAAe,KACf,kBAAkB,GAClB;QACA,OAAO,eAAe,IAAI,eAAe,CAAC,CAAC,EAAE,WAAW,UAAU,CAAC;IACrE;IAEA,IACE,aAAa,KACb,eAAe,KACf,eAAe,KACf,kBAAkB,GAClB;QACA,OAAO,eAAe,IAAI,YAAY,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC;IAC/D;IAEA,IACE,gBAAgB,KAChB,eAAe,KACf,eAAe,KACf,eAAe,GACf;QACA,OAAO,kBAAkB,IACrB,mBACA,CAAC,CAAC,EAAE,cAAc,cAAc,CAAC;IACvC;IAEA,oBAAoB;IACpB,OAAO;AACT;AAEe,SAAS,YAAY,EAClC,eAAe,EACf,aAAa,EACI;;IACjB,MAAM,EAAE,eAAe,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACvE,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;iDAAE,CAAC,QAAU,MAAM,IAAI;;IACtD,MAAM,EACJ,SAAS,EACT,+BAA+B;IAC/B,wBAAwB,EACxB,iBAAiB,EAClB,GAAG,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;IAExB,oCAAoC;IACpC,MAAM,uBAAuB;QAC3B,IAAI,aAAa,IAAI;YACnB,kBAAkB,YAAY,EAAE,EAC7B,IAAI,CAAC;YACJ,2CAA2C;YAC7C,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,oCAAoC;YAClD,yCAAyC;YAC3C;QACJ;IACF;IAEA,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,wCAAmC;gBACjC,OAAO,0BAA0B,GAAG;YACtC;YAEA,qBAAqB;YACrB;yCAAO;oBACL,wCAAmC;wBACjC,OAAO,0BAA0B,GAAG;oBACtC;gBACF;;QACF;gCAAG;QAAC,aAAa;KAAG;IAEpB,mDAAmD;IACnD,MAAM,wBAAwB;IAE9B,kCAAkC;IAClC,QAAQ,GAAG,CACT,2BACA,sBAAsB,GAAG,CAAC,CAAC,OAAS,CAAC;YACnC,IAAI,KAAK,IAAI,KAAK,UAAU,KAAK,KAAK,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE;YAC5D,MACE,KAAK,IAAI,KAAK,UACV,KAAK,KAAK,EAAE,OACZ,KAAK,OAAO,CAAC,QAAQ,EAAE;YAC7B,MAAM,KAAK,IAAI;QACjB,CAAC;IAGH,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,UAAY;wBAAC,WAAU;;;;;;kCACxB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;wBACT,OAAM;kCAEN,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIzB,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;2BAE7B,sBAAsB,MAAM,GAAG,IACjC,sBAAsB,GAAG,CAAC,CAAC,cAAc;oBACvC,oDAAoD;oBACpD,MAAM,sBAAsB,aAAa,IAAI,KAAK;oBAClD,MAAM,aAAa,sBACf,eAAe,OAAO,aAAa,KAAK,EAAE,MAC1C,oBAAoB,UACpB,iBAAiB,OAAO,aAAa,OAAO,CAAC,EAAE,IAC/C,oBAAoB;oBAExB,4DAA4D;oBAC5D,MAAM,YAAY,sBACd,CAAC,MAAM,EAAE,aAAa,KAAK,EAAE,GAAG,CAAC,EAAE,OAAO,GAC1C,CAAC,KAAK,EAAE,aAAa,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO;oBAE9C,qBACE,6LAAC;wBAEC,WAAW,CAAC,6DAA6D,EACvE,aAAa,eAAe,IAC5B;wBACF,SAAS;4BACP,IAAI,uBAAuB,aAAa,KAAK,EAAE;gCAC7C,cAAc,aAAa,KAAK,CAAC,EAAE;4BACrC,OAAO;gCACL,gBAAgB,aAAa,OAAO,CAAC,EAAE;4BACzC;wBACF;;0CAEA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDACV,KACE,sBACI,aAAa,KAAK,EAAE,aAAa,KACjC,aAAa,OAAO,CAAC,QAAQ,EAAE,qBAC/B;gDAEN,WAAU;;;;;;0DAEZ,6LAAC,qIAAA,CAAA,iBAAc;0DACZ,sBACG,aAAa,KAAK,EAAE,MAChB,UAAU,GAAG,GACd,iBAAiB,OACpB,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,OAAO;;;;;;;;;;;;oCAI3C,CAAC,qCACA;kDACG,aAAa,OAAO,CAAC,MAAM,iBAC1B,6LAAC;4CAAK,WAAU;;;;;mDACd,aAAa,OAAO,CAAC,QAAQ,EAAE,yBACjC,6LAAC;4CAAK,WAAU;;;;;mDACd;;;;;;;;0CAIV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DACV,sBACG,aAAa,KAAK,EAAE,OACpB,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,OAAO;;;;;;;;;;;0DAG/C,6LAAC;gDAAI,WAAU;;oDACZ,aAAa,WAAW,kBACvB,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EACf,aAAa,WAAW,CAAC,SAAS;;;;;;oDAIvC,aAAa,WAAW,GAAG,mBAC1B,6LAAC;wDAAK,WAAU;kEACb,aAAa,WAAW;;;;;;;;;;;;;;;;;;oCAKhC,aAAa,QAAQ,iBACpB,6LAAC;wCAAE,WAAU;;4CACV,uBACD,aAAa,WAAW,IACxB,aAAa,WAAW,CAAC,MAAM,GAAG,kBAChC;;oDACG,aAAa,WAAW,CAAC,EAAE,CAAC,QAAQ;oDAAC;oDACrC,aAAa,WAAW,CAAC,MAAM,GAAG,KACjC,CAAC,IAAI,EAAE,aAAa,WAAW,CAAC,MAAM,GAAG,EAAE,WAAW,CAAC;;+DAG3D;0DAEF,6LAAC;gDAAK,WAAU;;kEACd,6LAAC;wDAAK,WAAU;kEAAgC;;;;;;kEAChD,6LAAC;wDAAK,WAAU;kEAAkC;;;;;;kEAGlD,6LAAC;wDAAK,WAAU;kEAAkC;;;;;;;;;;;;;;;;;+CAKpD,aAAa,WAAW,iBAC1B,6LAAC;wCAAE,WAAU;kDACV,aAAa,WAAW,CAAC,QAAQ,GAC9B,6BAEA,CAAC,uBACD,aAAa,WAAW,CAAC,QAAQ,KAAK,aAAa,KAC/C,CAAC;4CACC,+CAA+C;4CAC/C,MAAM,cAAc,aAAa,WAAW;4CAC5C,MAAM,aACJ,YAAY,MAAM,EAAE,UAAU,YAC9B,mDAAmD;4CACnD,aAAa,KAAK,EAAE,aAAa,KAC/B,CAAC,IAAM,EAAE,EAAE,KAAK,YAAY,QAAQ,GACnC,YACH,qDAAqD;4CACrD,aAAa,KAAK,EAAE,SAAS,KAC3B,CAAC,IAAM,EAAE,MAAM,KAAK,YAAY,QAAQ,GACvC,MAAM,UAAU,YACnB;4CACF,OAAO,aAAa;wCACtB,CAAC,MACD,aAAa,WAAW,CAAC,QAAQ,KAC/B,aAAa,KACb,UACA,EAAE,IACR,CAAC,aAAa,WAAW,CAAC,OAAO,EAAE,QACjC,CAAC,aAAa,WAAW,CAAC,OAAO,EAAE,OAAO,SACtC,uBACE,aAAa,WAAW,CAAC,OAAO,CAAC,KAAK,IAExC,EAAE,CAAC;;;;;+CAEb,oCACF,6LAAC;wCAAE,WAAU;kDAAiC;;;;;+CAC5C,aAAa,OAAO,CAAC,QAAQ,EAAE,8BACjC,6LAAC;wCAAE,WAAU;kDACV,aAAa,OAAO,CAAC,QAAQ,CAAC,aAAa;;;;;+CAE5C;;;;;;;;uBAjID;;;;;gBAqIX,mBAEA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAMzC;GA5OwB;;QAIsC,6HAAA,CAAA,eAAY;QACpD,6HAAA,CAAA,eAAY;QAM5B,sIAAA,CAAA,wBAAqB;;;KAXH", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ChatHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo } from \"react\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Button } from \"@/components/ui/button\";\nimport { GroupRole, User, UserInfo } from \"@/types/base\";\nimport {\n  Info,\n  Search,\n  X,\n  ChevronLeft,\n  Users,\n  // Phone,\n  // Video,\n} from \"lucide-react\";\nimport { useChatStore } from \"@/stores/chatStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\n// Toast removed from header - handled at UI level only\n\nimport { Input } from \"@/components/ui/input\";\n\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\nimport CallButton from \"@/components/call/CallButton\";\n\ninterface ChatHeaderProps {\n  contact?:\n    | (User & { userInfo: UserInfo; online?: boolean; lastSeen?: Date })\n    | null;\n  group?: {\n    id: string;\n    name: string;\n    avatarUrl?: string | null;\n    createdAt?: Date;\n    memberIds?: string[];\n    memberUsers?: Array<{\n      id: string;\n      fullName: string;\n      profilePictureUrl?: string | null;\n      role: GroupRole;\n    }>;\n  } | null;\n  onToggleInfo: () => void;\n  onBackToList?: () => void;\n}\n\nexport default function ChatHeader({\n  contact,\n  group,\n  onToggleInfo,\n  onBackToList,\n}: ChatHeaderProps) {\n  const [isSearching, setIsSearching] = useState(false);\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\n  const {\n    searchText,\n    setSearchText,\n    searchMessages,\n    clearSearch,\n    currentChatType,\n  } = useChatStore();\n\n  // Lấy danh sách cuộc trò chuyện từ conversationsStore\n  const conversations = useConversationsStore((state) => state.conversations);\n\n  // Tìm thông tin nhóm từ conversationsStore\n  const groupConversation = useMemo(() => {\n    if (!group) return null;\n    return conversations.find(\n      (conv) => conv.type === \"GROUP\" && conv.group?.id === group.id,\n    );\n  }, [conversations, group]);\n\n  // Tính toán số lượng thành viên\n  const memberCount = useMemo(() => {\n    // Ưu tiên sử dụng thông tin từ conversationsStore\n    if (groupConversation?.group?.memberUsers) {\n      return groupConversation.group.memberUsers.length;\n    }\n    // Nếu không có, sử dụng thông tin từ group prop\n    return group?.memberUsers?.length || 0;\n  }, [groupConversation, group]);\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchText.trim()) {\n      searchMessages(searchText);\n    }\n  };\n\n  const toggleSearch = () => {\n    if (isSearching) {\n      clearSearch();\n    }\n    setIsSearching(!isSearching);\n  };\n\n  // If neither contact nor group is provided, show default header\n  if (!contact && !group) {\n    return (\n      <div className=\"border-b bg-white p-3 h-[69px] flex items-center justify-between\">\n        <h2 className=\"font-semibold\">Chọn một cuộc trò chuyện</h2>\n      </div>\n    );\n  }\n\n  // Các hàm handleCall và handleVideoCall đã được thay thế bằng component CallButton\n\n  return (\n    <>\n      <div className=\"bg-white border-b border-gray-200 p-3 h-[69px] flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          {onBackToList && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"mr-2 md:hidden\"\n              onClick={onBackToList}\n            >\n              <ChevronLeft className=\"h-5 w-5\" />\n            </Button>\n          )}\n          {contact ? (\n            <div\n              className=\"flex items-center cursor-pointer hover:bg-gray-100 p-1 rounded-md transition-colors\"\n              onClick={() => setShowProfileDialog(true)}\n            >\n              <div className=\"relative\">\n                <Avatar className=\"h-10 w-10 mr-3\">\n                  <AvatarImage\n                    src={contact.userInfo?.profilePictureUrl || undefined}\n                    className=\"object-cover\"\n                  />\n                  <AvatarFallback>\n                    {contact.userInfo?.fullName?.slice(0, 2).toUpperCase() ||\n                      \"??\"}\n                  </AvatarFallback>\n                </Avatar>\n                {/* Online status indicator */}\n                {contact.online && (\n                  <span className=\"absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white\"></span>\n                )}\n              </div>\n              <div>\n                <h2 className=\"text-sm font-semibold\">\n                  {contact.userInfo?.fullName || \"Người dùng\"}\n                </h2>\n                <p className=\"text-xs text-gray-500\">\n                  {contact.online\n                    ? \"Đang hoạt động\"\n                    : contact.lastSeen\n                      ? `Hoạt động ${new Date(contact.lastSeen).toLocaleString()}`\n                      : contact.userInfo?.statusMessage ||\n                        \"Không có trạng thái\"}\n                </p>\n              </div>\n            </div>\n          ) : group ? (\n            <div className=\"flex items-center hover:bg-gray-100 p-1 rounded-md transition-colors\">\n              <div className=\"relative\">\n                <Avatar className=\"h-10 w-10 mr-3\">\n                  <AvatarImage\n                    src={\n                      // Ưu tiên sử dụng thông tin từ conversationsStore\n                      groupConversation?.group?.avatarUrl ||\n                      group.avatarUrl ||\n                      undefined\n                    }\n                    className=\"object-cover\"\n                  />\n                  <AvatarFallback>\n                    {(groupConversation?.group?.name || group.name)\n                      ?.slice(0, 2)\n                      .toUpperCase() || \"GP\"}\n                  </AvatarFallback>\n                </Avatar>\n              </div>\n              <div>\n                <h2 className=\"text-sm font-semibold\">\n                  {groupConversation?.group?.name || group.name || \"Nhóm chat\"}\n                </h2>\n                <p className=\"text-xs text-gray-500 flex items-center\">\n                  <Users className=\"h-3 w-3 mr-1\" />\n                  {memberCount} thành viên\n                </p>\n              </div>\n            </div>\n          ) : null}\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {isSearching ? (\n            <form onSubmit={handleSearch} className=\"flex items-center\">\n              <Input\n                type=\"text\"\n                placeholder=\"Tìm kiếm tin nhắn...\"\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                className=\"h-8 w-48 mr-2\"\n                autoFocus\n              />\n              <Button\n                type=\"submit\"\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n              >\n                <Search className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={toggleSearch}\n                className=\"h-8 w-8\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </form>\n          ) : (\n            <>\n              {contact && (\n                <CallButton\n                  target={contact}\n                  targetType=\"USER\"\n                  variant=\"icon\"\n                  size=\"md\"\n                />\n              )}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"rounded-full\"\n                onClick={toggleSearch}\n              >\n                <Search className=\"h-5 w-5 text-gray-500\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"rounded-full\"\n                onClick={onToggleInfo}\n              >\n                <Info className=\"h-5 w-5 text-gray-500\" />\n              </Button>\n            </>\n          )}\n        </div>\n      </div>\n\n      {/* Profile Dialog - only show for user chats */}\n      {currentChatType === \"USER\" && contact && showProfileDialog && (\n        <ProfileDialog\n          user={contact}\n          isOpen={true}\n          onOpenChange={setShowProfileDialog}\n          isOwnProfile={false}\n        />\n      )}\n\n      {/* For group chats, we could add a GroupProfileDialog component here in the future */}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA,uDAAuD;AAEvD;AAEA;AACA;;;AAtBA;;;;;;;;;;AA6Ce,SAAS,WAAW,EACjC,OAAO,EACP,KAAK,EACL,YAAY,EACZ,YAAY,EACI;;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EACJ,UAAU,EACV,aAAa,EACb,cAAc,EACd,WAAW,EACX,eAAe,EAChB,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAEf,sDAAsD;IACtD,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;2DAAE,CAAC,QAAU,MAAM,aAAa;;IAE1E,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAChC,IAAI,CAAC,OAAO,OAAO;YACnB,OAAO,cAAc,IAAI;yDACvB,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO,MAAM,EAAE;;QAElE;gDAAG;QAAC;QAAe;KAAM;IAEzB,gCAAgC;IAChC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE;YAC1B,kDAAkD;YAClD,IAAI,mBAAmB,OAAO,aAAa;gBACzC,OAAO,kBAAkB,KAAK,CAAC,WAAW,CAAC,MAAM;YACnD;YACA,gDAAgD;YAChD,OAAO,OAAO,aAAa,UAAU;QACvC;0CAAG;QAAC;QAAmB;KAAM;IAE7B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI;YACrB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa;YACf;QACF;QACA,eAAe,CAAC;IAClB;IAEA,gEAAgE;IAChE,IAAI,CAAC,WAAW,CAAC,OAAO;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAG,WAAU;0BAAgB;;;;;;;;;;;IAGpC;IAEA,mFAAmF;IAEnF,qBACE;;0BACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;4BAG1B,wBACC,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,qBAAqB;;kDAEpC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAK,QAAQ,QAAQ,EAAE,qBAAqB;wDAC5C,WAAU;;;;;;kEAEZ,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,QAAQ,QAAQ,EAAE,UAAU,MAAM,GAAG,GAAG,iBACvC;;;;;;;;;;;;4CAIL,QAAQ,MAAM,kBACb,6LAAC;gDAAK,WAAU;;;;;;;;;;;;kDAGpB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ,EAAE,YAAY;;;;;;0DAEjC,6LAAC;gDAAE,WAAU;0DACV,QAAQ,MAAM,GACX,mBACA,QAAQ,QAAQ,GACd,CAAC,UAAU,EAAE,IAAI,KAAK,QAAQ,QAAQ,EAAE,cAAc,IAAI,GAC1D,QAAQ,QAAQ,EAAE,iBAClB;;;;;;;;;;;;;;;;;uCAIV,sBACF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KACE,kDAAkD;oDAClD,mBAAmB,OAAO,aAC1B,MAAM,SAAS,IACf;oDAEF,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;8DACZ,CAAC,mBAAmB,OAAO,QAAQ,MAAM,IAAI,GAC1C,MAAM,GAAG,GACV,iBAAiB;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,mBAAmB,OAAO,QAAQ,MAAM,IAAI,IAAI;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB;oDAAY;;;;;;;;;;;;;;;;;;uCAIjB;;;;;;;kCAEN,6LAAC;wBAAI,WAAU;kCACZ,4BACC,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;oCACV,SAAS;;;;;;8CAEX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;iDAIjB;;gCACG,yBACC,6LAAC,2IAAA,CAAA,UAAU;oCACT,QAAQ;oCACR,YAAW;oCACX,SAAQ;oCACR,MAAK;;;;;;8CAGT,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,oBAAoB,UAAU,WAAW,mCACxC,6LAAC,iJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc;;;;;;;;AAOxB;GAxNwB;;QAclB,6HAAA,CAAA,eAAY;QAGM,sIAAA,CAAA,wBAAqB;;;KAjBrB", "debugId": null}}, {"offset": {"line": 832, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/GroupChatHeader.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useMemo, useEffect, useCallback, useRef } from \"react\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Button } from \"@/components/ui/button\";\nimport { Group, GroupMember } from \"@/types/base\";\nimport {\n  Info,\n  Search,\n  X,\n  Users,\n  ChevronLeft,\n  AlertTriangle,\n  // Phone,\n  // Video,\n} from \"lucide-react\";\nimport { useChatStore } from \"@/stores/chatStore\";\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\nimport { useAuthStore } from \"@/stores/authStore\";\nimport { Input } from \"@/components/ui/input\";\nimport { toast } from \"sonner\";\nimport { getGroupById } from \"@/actions/group.action\";\nimport CallButton from \"@/components/call/CallButton\";\nimport GroupChatHeaderSocketHandler from \"@/components/group/GroupChatHeaderSocketHandler\";\n\ninterface GroupChatHeaderProps {\n  group: Group | null;\n  onToggleInfo: () => void;\n  onBackToList?: () => void;\n}\n\nexport default function GroupChatHeader({\n  group,\n  onToggleInfo,\n  onBackToList,\n}: GroupChatHeaderProps) {\n  const [isSearching, setIsSearching] = useState(false);\n  const [isCheckingMembership, setIsCheckingMembership] = useState(false);\n  const [membershipCheckInterval, setMembershipCheckInterval] =\n    useState<NodeJS.Timeout | null>(null);\n  const [forceUpdateCounter, setForceUpdateCounter] = useState(0);\n  const lastCheckTimestampRef = useRef<number>(0);\n  const API_THROTTLE_MS = 30000; // 30 giây\n\n  const { searchText, setSearchText, searchMessages, clearSearch } =\n    useChatStore();\n  const currentUser = useAuthStore((state) => state.user);\n\n  // Lấy danh sách cuộc trò chuyện từ conversationsStore\n  const conversations = useConversationsStore((state) => state.conversations);\n\n  // Tìm thông tin nhóm từ conversationsStore\n  const groupConversation = useMemo(() => {\n    if (!group) return null;\n    return conversations.find(\n      (conv) => conv.type === \"GROUP\" && conv.group?.id === group.id,\n    );\n  }, [conversations, group]);\n\n  // Callback để xử lý cập nhật từ socket\n  const handleGroupUpdated = useCallback(() => {\n    console.log(\n      \"[GroupChatHeader] Group updated via socket, forcing re-render\",\n    );\n    setForceUpdateCounter((prev) => prev + 1);\n  }, []);\n\n  // Tính toán số lượng thành viên\n  const memberCount = useMemo(() => {\n    // Ưu tiên sử dụng thông tin từ conversationsStore\n    if (groupConversation?.group?.memberUsers) {\n      return groupConversation.group.memberUsers.length;\n    }\n    // Nếu không có, sử dụng thông tin từ group prop\n    return group?.members?.length || 0;\n  }, [\n    groupConversation?.group?.memberUsers,\n    group?.members,\n    forceUpdateCounter,\n  ]);\n\n  // Hàm xử lý khi người dùng không còn là thành viên của nhóm\n  const handleUserNotInGroup = useCallback(\n    (groupId: string, userId: string, groupName: string) => {\n      // Hiển thị thông báo\n      toast.error(\"Bạn không còn là thành viên của nhóm này\", {\n        description: \"Bạn đã bị xóa khỏi nhóm hoặc nhóm đã bị giải tán\",\n        icon: <AlertTriangle className=\"h-5 w-5\" />,\n        duration: 5000,\n      });\n\n      // Xóa nhóm khỏi danh sách cuộc trò chuyện\n      const conversationsStore = useConversationsStore.getState();\n      conversationsStore.checkAndRemoveGroups(groupId, groupName);\n\n      // Xóa tin nhắn của nhóm khỏi cache\n      useChatStore.getState().clearChatCache(\"GROUP\", groupId);\n\n      // Chuyển về trạng thái không chọn nhóm\n      useChatStore.getState().setSelectedGroup(null);\n\n      // Tải lại danh sách cuộc trò chuyện\n      if (userId) {\n        conversationsStore.loadConversations(userId);\n      }\n\n      // Xóa interval kiểm tra\n      if (membershipCheckInterval) {\n        clearInterval(membershipCheckInterval);\n        setMembershipCheckInterval(null);\n      }\n    },\n    [membershipCheckInterval],\n  );\n\n  // Hàm kiểm tra xem người dùng hiện tại có còn là thành viên của nhóm không\n  const checkGroupMembership = useCallback(async () => {\n    // Store the current group and user IDs to avoid closure issues\n    const currentGroupId = group?.id;\n    const currentUserId = currentUser?.id;\n\n    if (!currentGroupId || !currentUserId || isCheckingMembership) return;\n\n    // Kiểm tra throttle để giảm số lượng request API\n    const now = Date.now();\n    if (now - lastCheckTimestampRef.current < API_THROTTLE_MS) {\n      console.log(\n        `[GroupChatHeader] Skipping membership check due to throttling. Last check was ${(now - lastCheckTimestampRef.current) / 1000}s ago`,\n      );\n      return;\n    }\n\n    try {\n      setIsCheckingMembership(true);\n      console.log(\n        `[GroupChatHeader] Checking if user ${currentUserId} is still a member of group ${currentGroupId}`,\n      );\n\n      // Cập nhật timestamp trước khi gọi API\n      lastCheckTimestampRef.current = now;\n\n      // Ưu tiên sử dụng thông tin từ conversationsStore\n      const conversationsStore = useConversationsStore.getState();\n      const groupConversation = conversationsStore.conversations.find(\n        (conv) => conv.type === \"GROUP\" && conv.group?.id === currentGroupId,\n      );\n\n      // Nếu có thông tin nhóm trong conversationsStore, sử dụng nó\n      if (groupConversation?.group) {\n        const isMember =\n          groupConversation.group.memberIds?.includes(currentUserId) ||\n          groupConversation.group.memberUsers?.some(\n            (member) => member.id === currentUserId,\n          );\n\n        if (!isMember) {\n          console.log(\n            `[GroupChatHeader] User ${currentUserId} is no longer a member of group ${currentGroupId} (from conversationsStore)`,\n          );\n          handleUserNotInGroup(\n            currentGroupId,\n            currentUserId,\n            groupConversation.group.name,\n          );\n          return;\n        }\n\n        console.log(\n          `[GroupChatHeader] User ${currentUserId} is still a member of group ${currentGroupId} (from conversationsStore)`,\n        );\n        return;\n      }\n\n      // Nếu không có thông tin từ conversationsStore, kiểm tra cache\n      const chatStore = useChatStore.getState();\n      const cachedData =\n        chatStore.groupCache && chatStore.groupCache[currentGroupId];\n      const currentTime = new Date();\n      const isCacheValid =\n        cachedData &&\n        currentTime.getTime() - cachedData.lastFetched.getTime() < 30 * 1000; // 30 seconds cache\n\n      if (isCacheValid && cachedData.group) {\n        console.log(\n          `[GroupChatHeader] Using cached group data for membership check`,\n        );\n\n        const isMember =\n          cachedData.group.members?.some(\n            (member) => member.userId === currentUserId,\n          ) ||\n          cachedData.group.memberUsers?.some(\n            (member) => member.id === currentUserId,\n          );\n\n        if (!isMember) {\n          console.log(\n            `[GroupChatHeader] User ${currentUserId} is no longer a member of group ${currentGroupId} (from cache)`,\n          );\n          handleUserNotInGroup(\n            currentGroupId,\n            currentUserId,\n            cachedData.group.name,\n          );\n          return;\n        }\n\n        console.log(\n          `[GroupChatHeader] User ${currentUserId} is still a member of group ${currentGroupId} (from cache)`,\n        );\n        return;\n      }\n\n      // Nếu không có thông tin từ cache hoặc conversationsStore, gọi API\n      console.log(\n        `[GroupChatHeader] Fetching fresh group data for membership check`,\n      );\n      const result = await getGroupById(currentGroupId);\n      if (result.success && result.group) {\n        // Update the cache\n        if (chatStore.groupCache) {\n          chatStore.groupCache[currentGroupId] = {\n            group: result.group,\n            lastFetched: new Date(),\n          };\n        }\n\n        // Kiểm tra xem người dùng hiện tại có trong danh sách thành viên không\n        const isMember =\n          result.group.memberIds?.includes(currentUserId) ||\n          result.group.memberUsers?.some(\n            (member) => member.id === currentUserId,\n          );\n\n        if (!isMember) {\n          console.log(\n            `[GroupChatHeader] User ${currentUserId} is no longer a member of group ${currentGroupId} (from API)`,\n          );\n          handleUserNotInGroup(\n            currentGroupId,\n            currentUserId,\n            result.group.name,\n          );\n        } else {\n          console.log(\n            `[GroupChatHeader] User ${currentUserId} is still a member of group ${currentGroupId} (from API)`,\n          );\n        }\n      }\n    } catch (error) {\n      console.error(\n        `[GroupChatHeader] Error checking group membership:`,\n        error,\n      );\n    } finally {\n      setIsCheckingMembership(false);\n    }\n  }, [isCheckingMembership, handleUserNotInGroup]);\n\n  // Thiết lập interval kiểm tra thành viên nhóm\n  useEffect(() => {\n    // Thêm throttle để tránh thiết lập interval quá thường xuyên\n    if (!window._lastGroupHeaderIntervalTime) {\n      window._lastGroupHeaderIntervalTime = {};\n    }\n\n    // Chỉ thiết lập interval nếu có nhóm được chọn\n    if (group?.id && currentUser?.id) {\n      const groupId = group.id;\n      const now = Date.now();\n      const lastIntervalTime =\n        window._lastGroupHeaderIntervalTime[groupId] || 0;\n      const timeSinceLastInterval = now - lastIntervalTime;\n\n      // Nếu đã thiết lập interval trong vòng 2 giây, bỏ qua\n      if (timeSinceLastInterval < 2000) {\n        console.log(\n          `[GroupChatHeader] Skipping interval setup, last setup was ${timeSinceLastInterval}ms ago`,\n        );\n        return;\n      }\n\n      // Cập nhật thời gian thiết lập interval\n      window._lastGroupHeaderIntervalTime[groupId] = now;\n\n      console.log(\n        `[GroupChatHeader] Setting up membership check interval for group ${groupId}`,\n      );\n\n      // Kiểm tra ngay lập tức, nhưng chỉ nếu đã quá thời gian throttle\n      if (now - lastCheckTimestampRef.current >= API_THROTTLE_MS) {\n        // Sử dụng setTimeout để tránh gọi checkGroupMembership trực tiếp trong useEffect\n        setTimeout(() => {\n          if (group?.id === groupId) {\n            // Kiểm tra lại để đảm bảo nhóm không thay đổi\n            checkGroupMembership();\n          }\n        }, 100);\n      } else {\n        console.log(\n          `[GroupChatHeader] Skipping initial membership check due to throttling. Last check was ${(now - lastCheckTimestampRef.current) / 1000}s ago`,\n        );\n      }\n\n      // Thiết lập interval kiểm tra mỗi 300 giây (5 phút) để giảm số lượng API calls\n      const intervalId = setInterval(() => {\n        // Kiểm tra xem nhóm hiện tại có còn là nhóm ban đầu không\n        if (group?.id === groupId) {\n          checkGroupMembership();\n        }\n      }, 300000);\n\n      // Lưu intervalId vào state\n      setMembershipCheckInterval(intervalId);\n\n      // Cleanup khi component unmount hoặc nhóm thay đổi\n      return () => {\n        console.log(\n          `[GroupChatHeader] Cleaning up membership check interval for group ${groupId}`,\n        );\n        clearInterval(intervalId);\n        setMembershipCheckInterval(null);\n      };\n    }\n    // Loại bỏ checkGroupMembership khỏi dependencies để tránh vòng lặp vô hạn\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [group?.id, currentUser?.id]);\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (searchText.trim()) {\n      searchMessages(searchText);\n    }\n  };\n\n  const toggleSearch = () => {\n    if (isSearching) {\n      clearSearch();\n    }\n    setIsSearching(!isSearching);\n  };\n\n  if (!group) {\n    return (\n      <div className=\"border-b bg-white p-3 h-[69px] flex items-center justify-between\">\n        <h2 className=\"font-semibold\">Chọn một cuộc trò chuyện</h2>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {/* Socket handler for real-time updates */}\n      {group && (\n        <GroupChatHeaderSocketHandler\n          groupId={group.id}\n          onGroupUpdated={handleGroupUpdated}\n        />\n      )}\n      <div className=\"bg-white border-b border-gray-200 p-3 h-[69px] flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          {onBackToList && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"mr-2 md:hidden\"\n              onClick={onBackToList}\n            >\n              <ChevronLeft className=\"h-5 w-5\" />\n            </Button>\n          )}\n          <div\n            className=\"flex items-center cursor-pointer hover:bg-gray-100 p-1 rounded-md transition-colors\"\n            onClick={onToggleInfo}\n          >\n            <div className=\"relative\">\n              <Avatar className=\"h-10 w-10 mr-3\">\n                <AvatarImage\n                  src={\n                    // Ưu tiên sử dụng thông tin từ conversationsStore\n                    groupConversation?.group?.avatarUrl ||\n                    group.avatarUrl ||\n                    undefined\n                  }\n                  className=\"object-cover\"\n                />\n                <AvatarFallback>\n                  {(groupConversation?.group?.name || group.name)\n                    ?.slice(0, 2)\n                    .toUpperCase() || \"GR\"}\n                </AvatarFallback>\n              </Avatar>\n            </div>\n            <div>\n              <h2 className=\"text-sm font-semibold\">\n                {groupConversation?.group?.name || group.name || \"Nhóm chat\"}\n              </h2>\n              <p className=\"text-xs text-gray-500 flex items-center\">\n                <Users className=\"h-3 w-3 mr-1\" />\n                {memberCount} thành viên\n              </p>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          {isSearching ? (\n            <form onSubmit={handleSearch} className=\"flex items-center\">\n              <Input\n                type=\"text\"\n                placeholder=\"Tìm kiếm tin nhắn...\"\n                value={searchText}\n                onChange={(e) => setSearchText(e.target.value)}\n                className=\"h-8 w-48 mr-2\"\n                autoFocus\n              />\n              <Button\n                type=\"submit\"\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"h-8 w-8\"\n              >\n                <Search className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                type=\"button\"\n                variant=\"ghost\"\n                size=\"icon\"\n                onClick={toggleSearch}\n                className=\"h-8 w-8\"\n              >\n                <X className=\"h-4 w-4\" />\n              </Button>\n            </form>\n          ) : (\n            <>\n              {/* Nút gọi điện và gọi video */}\n              {group && (\n                <CallButton\n                  target={group}\n                  targetType=\"GROUP\"\n                  variant=\"icon\"\n                  size=\"md\"\n                />\n              )}\n\n              {/* Nút tìm kiếm */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"rounded-full\"\n                onClick={toggleSearch}\n              >\n                <Search className=\"h-5 w-5 text-gray-500\" />\n              </Button>\n\n              {/* Nút thông tin */}\n              <Button\n                variant=\"ghost\"\n                size=\"icon\"\n                className=\"rounded-full\"\n                onClick={onToggleInfo}\n              >\n                <Info className=\"h-5 w-5 text-gray-500\" />\n              </Button>\n            </>\n          )}\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAvBA;;;;;;;;;;;;;AA+Be,SAAS,gBAAgB,EACtC,KAAK,EACL,YAAY,EACZ,YAAY,EACS;;IACrB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,yBAAyB,2BAA2B,GACzD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAClC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC7C,MAAM,kBAAkB,OAAO,UAAU;IAEzC,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,WAAW,EAAE,GAC9D,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACb,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;qDAAE,CAAC,QAAU,MAAM,IAAI;;IAEtD,sDAAsD;IACtD,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;gEAAE,CAAC,QAAU,MAAM,aAAa;;IAE1E,2CAA2C;IAC3C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sDAAE;YAChC,IAAI,CAAC,OAAO,OAAO;YACnB,OAAO,cAAc,IAAI;8DACvB,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO,MAAM,EAAE;;QAElE;qDAAG;QAAC;QAAe;KAAM;IAEzB,uCAAuC;IACvC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACrC,QAAQ,GAAG,CACT;YAEF;mEAAsB,CAAC,OAAS,OAAO;;QACzC;0DAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE;YAC1B,kDAAkD;YAClD,IAAI,mBAAmB,OAAO,aAAa;gBACzC,OAAO,kBAAkB,KAAK,CAAC,WAAW,CAAC,MAAM;YACnD;YACA,gDAAgD;YAChD,OAAO,OAAO,SAAS,UAAU;QACnC;+CAAG;QACD,mBAAmB,OAAO;QAC1B,OAAO;QACP;KACD;IAED,4DAA4D;IAC5D,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DACrC,CAAC,SAAiB,QAAgB;YAChC,qBAAqB;YACrB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,4CAA4C;gBACtD,aAAa;gBACb,oBAAM,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAC/B,UAAU;YACZ;YAEA,0CAA0C;YAC1C,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;YACzD,mBAAmB,oBAAoB,CAAC,SAAS;YAEjD,mCAAmC;YACnC,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,cAAc,CAAC,SAAS;YAEhD,uCAAuC;YACvC,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAEzC,oCAAoC;YACpC,IAAI,QAAQ;gBACV,mBAAmB,iBAAiB,CAAC;YACvC;YAEA,wBAAwB;YACxB,IAAI,yBAAyB;gBAC3B,cAAc;gBACd,2BAA2B;YAC7B;QACF;4DACA;QAAC;KAAwB;IAG3B,2EAA2E;IAC3E,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACvC,+DAA+D;YAC/D,MAAM,iBAAiB,OAAO;YAC9B,MAAM,gBAAgB,aAAa;YAEnC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,sBAAsB;YAE/D,iDAAiD;YACjD,MAAM,MAAM,KAAK,GAAG;YACpB,IAAI,MAAM,sBAAsB,OAAO,GAAG,iBAAiB;gBACzD,QAAQ,GAAG,CACT,CAAC,8EAA8E,EAAE,CAAC,MAAM,sBAAsB,OAAO,IAAI,KAAK,KAAK,CAAC;gBAEtI;YACF;YAEA,IAAI;gBACF,wBAAwB;gBACxB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,cAAc,4BAA4B,EAAE,gBAAgB;gBAGpG,uCAAuC;gBACvC,sBAAsB,OAAO,GAAG;gBAEhC,kDAAkD;gBAClD,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;gBACzD,MAAM,oBAAoB,mBAAmB,aAAa,CAAC,IAAI;2FAC7D,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO;;gBAGxD,6DAA6D;gBAC7D,IAAI,mBAAmB,OAAO;oBAC5B,MAAM,WACJ,kBAAkB,KAAK,CAAC,SAAS,EAAE,SAAS,kBAC5C,kBAAkB,KAAK,CAAC,WAAW,EAAE;6EACnC,CAAC,SAAW,OAAO,EAAE,KAAK;;oBAG9B,IAAI,CAAC,UAAU;wBACb,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,gCAAgC,EAAE,eAAe,0BAA0B,CAAC;wBAEtH,qBACE,gBACA,eACA,kBAAkB,KAAK,CAAC,IAAI;wBAE9B;oBACF;oBAEA,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,4BAA4B,EAAE,eAAe,0BAA0B,CAAC;oBAElH;gBACF;gBAEA,+DAA+D;gBAC/D,MAAM,YAAY,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBACvC,MAAM,aACJ,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,eAAe;gBAC9D,MAAM,cAAc,IAAI;gBACxB,MAAM,eACJ,cACA,YAAY,OAAO,KAAK,WAAW,WAAW,CAAC,OAAO,KAAK,KAAK,MAAM,mBAAmB;gBAE3F,IAAI,gBAAgB,WAAW,KAAK,EAAE;oBACpC,QAAQ,GAAG,CACT,CAAC,8DAA8D,CAAC;oBAGlE,MAAM,WACJ,WAAW,KAAK,CAAC,OAAO,EAAE;6EACxB,CAAC,SAAW,OAAO,MAAM,KAAK;+EAEhC,WAAW,KAAK,CAAC,WAAW,EAAE;6EAC5B,CAAC,SAAW,OAAO,EAAE,KAAK;;oBAG9B,IAAI,CAAC,UAAU;wBACb,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,gCAAgC,EAAE,eAAe,aAAa,CAAC;wBAEzG,qBACE,gBACA,eACA,WAAW,KAAK,CAAC,IAAI;wBAEvB;oBACF;oBAEA,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,4BAA4B,EAAE,eAAe,aAAa,CAAC;oBAErG;gBACF;gBAEA,mEAAmE;gBACnE,QAAQ,GAAG,CACT,CAAC,gEAAgE,CAAC;gBAEpE,MAAM,SAAS,MAAM,CAAA,GAAA,oIAAA,CAAA,eAAY,AAAD,EAAE;gBAClC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,EAAE;oBAClC,mBAAmB;oBACnB,IAAI,UAAU,UAAU,EAAE;wBACxB,UAAU,UAAU,CAAC,eAAe,GAAG;4BACrC,OAAO,OAAO,KAAK;4BACnB,aAAa,IAAI;wBACnB;oBACF;oBAEA,uEAAuE;oBACvE,MAAM,WACJ,OAAO,KAAK,CAAC,SAAS,EAAE,SAAS,kBACjC,OAAO,KAAK,CAAC,WAAW,EAAE;6EACxB,CAAC,SAAW,OAAO,EAAE,KAAK;;oBAG9B,IAAI,CAAC,UAAU;wBACb,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,gCAAgC,EAAE,eAAe,WAAW,CAAC;wBAEvG,qBACE,gBACA,eACA,OAAO,KAAK,CAAC,IAAI;oBAErB,OAAO;wBACL,QAAQ,GAAG,CACT,CAAC,uBAAuB,EAAE,cAAc,4BAA4B,EAAE,eAAe,WAAW,CAAC;oBAErG;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CACX,CAAC,kDAAkD,CAAC,EACpD;YAEJ,SAAU;gBACR,wBAAwB;YAC1B;QACF;4DAAG;QAAC;QAAsB;KAAqB;IAE/C,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,6DAA6D;YAC7D,IAAI,CAAC,OAAO,4BAA4B,EAAE;gBACxC,OAAO,4BAA4B,GAAG,CAAC;YACzC;YAEA,+CAA+C;YAC/C,IAAI,OAAO,MAAM,aAAa,IAAI;gBAChC,MAAM,UAAU,MAAM,EAAE;gBACxB,MAAM,MAAM,KAAK,GAAG;gBACpB,MAAM,mBACJ,OAAO,4BAA4B,CAAC,QAAQ,IAAI;gBAClD,MAAM,wBAAwB,MAAM;gBAEpC,sDAAsD;gBACtD,IAAI,wBAAwB,MAAM;oBAChC,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,sBAAsB,MAAM,CAAC;oBAE5F;gBACF;gBAEA,wCAAwC;gBACxC,OAAO,4BAA4B,CAAC,QAAQ,GAAG;gBAE/C,QAAQ,GAAG,CACT,CAAC,iEAAiE,EAAE,SAAS;gBAG/E,iEAAiE;gBACjE,IAAI,MAAM,sBAAsB,OAAO,IAAI,iBAAiB;oBAC1D,iFAAiF;oBACjF;qDAAW;4BACT,IAAI,OAAO,OAAO,SAAS;gCACzB,8CAA8C;gCAC9C;4BACF;wBACF;oDAAG;gBACL,OAAO;oBACL,QAAQ,GAAG,CACT,CAAC,sFAAsF,EAAE,CAAC,MAAM,sBAAsB,OAAO,IAAI,KAAK,KAAK,CAAC;gBAEhJ;gBAEA,+EAA+E;gBAC/E,MAAM,aAAa;4DAAY;wBAC7B,0DAA0D;wBAC1D,IAAI,OAAO,OAAO,SAAS;4BACzB;wBACF;oBACF;2DAAG;gBAEH,2BAA2B;gBAC3B,2BAA2B;gBAE3B,mDAAmD;gBACnD;iDAAO;wBACL,QAAQ,GAAG,CACT,CAAC,kEAAkE,EAAE,SAAS;wBAEhF,cAAc;wBACd,2BAA2B;oBAC7B;;YACF;QACA,0EAA0E;QAC1E,uDAAuD;QACzD;oCAAG;QAAC,OAAO;QAAI,aAAa;KAAG;IAE/B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,WAAW,IAAI,IAAI;YACrB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa;YACf;QACF;QACA,eAAe,CAAC;IAClB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAG,WAAU;0BAAgB;;;;;;;;;;;IAGpC;IAEA,qBACE;;YAEG,uBACC,6LAAC,8JAAA,CAAA,UAA4B;gBAC3B,SAAS,MAAM,EAAE;gBACjB,gBAAgB;;;;;;0BAGpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,8BACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAG3B,6LAAC;gCACC,WAAU;gCACV,SAAS;;kDAET,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDACV,KACE,kDAAkD;oDAClD,mBAAmB,OAAO,aAC1B,MAAM,SAAS,IACf;oDAEF,WAAU;;;;;;8DAEZ,6LAAC,qIAAA,CAAA,iBAAc;8DACZ,CAAC,mBAAmB,OAAO,QAAQ,MAAM,IAAI,GAC1C,MAAM,GAAG,GACV,iBAAiB;;;;;;;;;;;;;;;;;kDAI1B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DACX,mBAAmB,OAAO,QAAQ,MAAM,IAAI,IAAI;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;kEACX,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAChB;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;kCAKrB,6LAAC;wBAAI,WAAU;kCACZ,4BACC,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAU;oCACV,SAAS;;;;;;8CAEX,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;8CAEV,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;iDAIjB;;gCAEG,uBACC,6LAAC,2IAAA,CAAA,UAAU;oCACT,QAAQ;oCACR,YAAW;oCACX,SAAQ;oCACR,MAAK;;;;;;8CAKT,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAIpB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;8CAET,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GAvbwB;;QAcpB,6HAAA,CAAA,eAAY;QACM,6HAAA,CAAA,eAAY;QAGV,sIAAA,CAAA,wBAAqB;;;KAlBrB", "debugId": null}}, {"offset": {"line": 1388, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/MediaGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Media } from \"@/types/base\";\r\nimport Image from \"next/image\";\r\nimport { Download, FileText, Play, Music } from \"lucide-react\";\r\n\r\ninterface MediaGridProps {\r\n  media: Media[];\r\n  onDownload: (media: Media) => void;\r\n  onClick?: () => void;\r\n}\r\n\r\nexport default function MediaGrid({\r\n  media,\r\n  onDownload,\r\n  onClick,\r\n}: MediaGridProps) {\r\n  const images = media.filter((item) => item.type === \"IMAGE\");\r\n  const videos = media.filter((item) => item.type === \"VIDEO\");\r\n  const audios = media.filter((item) => item.type === \"AUDIO\");\r\n  const files = media.filter(\r\n    (item) =>\r\n      item.type !== \"IMAGE\" && item.type !== \"VIDEO\" && item.type !== \"AUDIO\",\r\n  );\r\n\r\n  const gridMedia = [...images, ...videos, ...audios, ...files];\r\n\r\n  gridMedia.sort((a, b) => {\r\n    if (a.type === \"IMAGE\" && b.type !== \"IMAGE\") return -1;\r\n    if (a.type !== \"IMAGE\" && b.type === \"IMAGE\") return 1;\r\n    if (a.type === \"VIDEO\" && b.type !== \"VIDEO\" && b.type !== \"IMAGE\")\r\n      return -1;\r\n    if (a.type !== \"VIDEO\" && a.type !== \"IMAGE\" && b.type === \"VIDEO\")\r\n      return 1;\r\n    if (\r\n      a.type === \"AUDIO\" &&\r\n      b.type !== \"AUDIO\" &&\r\n      b.type !== \"VIDEO\" &&\r\n      b.type !== \"IMAGE\"\r\n    )\r\n      return -1;\r\n    if (\r\n      a.type !== \"AUDIO\" &&\r\n      a.type !== \"VIDEO\" &&\r\n      a.type !== \"IMAGE\" &&\r\n      b.type === \"AUDIO\"\r\n    )\r\n      return 1;\r\n    return 0;\r\n  });\r\n\r\n  // Xác định layout grid dựa trên số lượng media\r\n  const getGridLayout = (count: number) => {\r\n    if (count === 1)\r\n      return {\r\n        cols: \"grid-cols-1\",\r\n        itemClass: \"aspect-video\",\r\n      };\r\n    if (count === 2)\r\n      return {\r\n        cols: \"grid-cols-2\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    if (count === 3)\r\n      return {\r\n        cols: \"grid-cols-3\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    if (count === 4)\r\n      return {\r\n        cols: \"grid-cols-2\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    if (count <= 6)\r\n      return {\r\n        cols: \"grid-cols-3\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    if (count <= 9)\r\n      return {\r\n        cols: \"grid-cols-3\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    if (count <= 12)\r\n      return {\r\n        cols: \"grid-cols-4\",\r\n        itemClass: \"aspect-square\",\r\n      };\r\n    return {\r\n      cols: \"grid-cols-5\",\r\n      itemClass: \"aspect-square\",\r\n    };\r\n  };\r\n\r\n  // Xác định kích thước cho item đầu tiên\r\n  const getFirstItemClass = (total: number) => {\r\n    if (total <= 3) return \"\";\r\n    if (total >= 4) return \"col-span-2 row-span-2\";\r\n    return \"\";\r\n  };\r\n\r\n  const maxDisplay = 16; // Giới hạn tối đa 16 items\r\n  const hasMore = gridMedia.length > maxDisplay;\r\n  const displayMedia = gridMedia.slice(0, maxDisplay);\r\n  const layout = getGridLayout(displayMedia.length);\r\n\r\n  return (\r\n    <div className={`grid gap-1 ${layout.cols} w-full`}>\r\n      {displayMedia.map((item, index) => (\r\n        <div\r\n          key={item.fileId}\r\n          className={`relative overflow-hidden rounded-md ${layout.itemClass} ${\r\n            index === 0 ? getFirstItemClass(displayMedia.length) : \"\"\r\n          }`}\r\n        >\r\n          {item.type === \"IMAGE\" ? (\r\n            <div\r\n              className=\"w-full h-full cursor-pointer isolate\"\r\n              onClick={onClick}\r\n            >\r\n              <Image\r\n                src={item.url}\r\n                alt={item.fileName}\r\n                className=\"object-cover w-full h-full\"\r\n                width={200}\r\n                height={200}\r\n                unoptimized\r\n              />\r\n              <div className=\"absolute inset-0 bg-black/0 hover:bg-black/20 transition-all duration-200\" />\r\n              <button\r\n                className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  onDownload(item);\r\n                }}\r\n              >\r\n                <Download className=\"h-4 w-4\" />\r\n              </button>\r\n            </div>\r\n          ) : item.type === \"VIDEO\" ? (\r\n            <div className=\"w-full h-full cursor-pointer isolate\">\r\n              <div className=\"w-full h-full relative\">\r\n                {item.thumbnailUrl ? (\r\n                  <Image\r\n                    src={item.thumbnailUrl}\r\n                    alt={item.fileName}\r\n                    className=\"object-cover w-full h-full\"\r\n                    width={200}\r\n                    height={200}\r\n                    unoptimized\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full bg-gray-800 flex items-center justify-center\">\r\n                    <Play className=\"h-8 w-8 text-white\" />\r\n                  </div>\r\n                )}\r\n                <div className=\"absolute inset-0 bg-black/30 flex items-center justify-center hover:bg-black/50 transition-all duration-200\">\r\n                  <Play className=\"h-10 w-10 text-white\" />\r\n                </div>\r\n                <button\r\n                  className=\"absolute bottom-2 right-2 bg-white/80 p-1 rounded-full shadow-sm hover:bg-white/100 transition-opacity opacity-0 hover:opacity-100\"\r\n                  onClick={(e) => {\r\n                    e.preventDefault();\r\n                    e.stopPropagation();\r\n                    onDownload(item);\r\n                  }}\r\n                >\r\n                  <Download className=\"h-4 w-4\" />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          ) : item.type === \"AUDIO\" ? (\r\n            <div className=\"w-full h-full bg-gray-100 flex flex-col items-center justify-center p-2 hover:bg-gray-200 transition-colors duration-200 isolate\">\r\n              <Music className=\"h-8 w-8 text-blue-500 mb-1\" />\r\n              <span className=\"text-xs text-gray-700 font-medium truncate w-full text-center\">\r\n                {item.fileName}\r\n              </span>\r\n              <span className=\"text-xs text-gray-500\">Audio</span>\r\n              <button\r\n                className=\"mt-2 bg-white p-1 rounded-full shadow hover:bg-gray-100 transition-opacity opacity-0 hover:opacity-100\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  onDownload(item);\r\n                }}\r\n              >\r\n                <Download className=\"h-4 w-4\" />\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"w-full h-full bg-gray-100 flex flex-col items-center justify-center p-2 hover:bg-gray-200 transition-colors duration-200 isolate\">\r\n              <FileText className=\"h-8 w-8 text-gray-500 mb-1\" />\r\n              <span className=\"text-xs text-gray-700 font-medium truncate w-full text-center\">\r\n                {item.fileName}\r\n              </span>\r\n              <span className=\"text-xs text-gray-500\">\r\n                {item.metadata.sizeFormatted}\r\n              </span>\r\n              <button\r\n                className=\"mt-2 bg-white p-1 rounded-full shadow hover:bg-gray-100 transition-opacity opacity-0 hover:opacity-100\"\r\n                onClick={(e) => {\r\n                  e.preventDefault();\r\n                  e.stopPropagation();\r\n                  onDownload(item);\r\n                }}\r\n              >\r\n                <Download className=\"h-4 w-4\" />\r\n              </button>\r\n            </div>\r\n          )}\r\n\r\n          {/* Hiển thị số lượng ảnh còn lại */}\r\n          {index === maxDisplay - 1 && hasMore && (\r\n            <div\r\n              className=\"absolute inset-0 bg-black/60 flex items-center justify-center text-white font-bold text-xl cursor-pointer hover:bg-black/70 transition-colors\"\r\n              onClick={onClick}\r\n            >\r\n              +{gridMedia.length - maxDisplay}\r\n            </div>\r\n          )}\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAYe,SAAS,UAAU,EAChC,KAAK,EACL,UAAU,EACV,OAAO,EACQ;IACf,MAAM,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;IACpD,MAAM,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;IACpD,MAAM,SAAS,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;IACpD,MAAM,QAAQ,MAAM,MAAM,CACxB,CAAC,OACC,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,KAAK;IAGpE,MAAM,YAAY;WAAI;WAAW;WAAW;WAAW;KAAM;IAE7D,UAAU,IAAI,CAAC,CAAC,GAAG;QACjB,IAAI,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,SAAS,OAAO,CAAC;QACtD,IAAI,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,SAAS,OAAO;QACrD,IAAI,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,SACzD,OAAO,CAAC;QACV,IAAI,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,WAAW,EAAE,IAAI,KAAK,SACzD,OAAO;QACT,IACE,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,SAEX,OAAO,CAAC;QACV,IACE,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,WACX,EAAE,IAAI,KAAK,SAEX,OAAO;QACT,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU,GACZ,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,UAAU,GACZ,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,UAAU,GACZ,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,UAAU,GACZ,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,SAAS,GACX,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,SAAS,GACX,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,IAAI,SAAS,IACX,OAAO;YACL,MAAM;YACN,WAAW;QACb;QACF,OAAO;YACL,MAAM;YACN,WAAW;QACb;IACF;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,CAAC;QACzB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,aAAa,IAAI,2BAA2B;IAClD,MAAM,UAAU,UAAU,MAAM,GAAG;IACnC,MAAM,eAAe,UAAU,KAAK,CAAC,GAAG;IACxC,MAAM,SAAS,cAAc,aAAa,MAAM;IAEhD,qBACE,6LAAC;QAAI,WAAW,CAAC,WAAW,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC;kBAC/C,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gBAEC,WAAW,CAAC,oCAAoC,EAAE,OAAO,SAAS,CAAC,CAAC,EAClE,UAAU,IAAI,kBAAkB,aAAa,MAAM,IAAI,IACvD;;oBAED,KAAK,IAAI,KAAK,wBACb,6LAAC;wBACC,WAAU;wBACV,SAAS;;0CAET,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,KAAK,GAAG;gCACb,KAAK,KAAK,QAAQ;gCAClB,WAAU;gCACV,OAAO;gCACP,QAAQ;gCACR,WAAW;;;;;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCACC,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB,WAAW;gCACb;0CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;+BAGtB,KAAK,IAAI,KAAK,wBAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,KAAK,YAAY,iBAChB,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,KAAK,YAAY;oCACtB,KAAK,KAAK,QAAQ;oCAClB,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,WAAW;;;;;yDAGb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAGpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;oCACC,WAAU;oCACV,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,EAAE,eAAe;wCACjB,WAAW;oCACb;8CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;+BAIxB,KAAK,IAAI,KAAK,wBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;0CAEhB,6LAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,6LAAC;gCACC,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB,WAAW;gCACb;0CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;6CAIxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,iNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ;;;;;;0CAEhB,6LAAC;gCAAK,WAAU;0CACb,KAAK,QAAQ,CAAC,aAAa;;;;;;0CAE9B,6LAAC;gCACC,WAAU;gCACV,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,EAAE,eAAe;oCACjB,WAAW;gCACb;0CAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMzB,UAAU,aAAa,KAAK,yBAC3B,6LAAC;wBACC,WAAU;wBACV,SAAS;;4BACV;4BACG,UAAU,MAAM,GAAG;;;;;;;;eA5GpB,KAAK,MAAM;;;;;;;;;;AAmH1B;KArNwB", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ForwardMessageDialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>eader,\r\n  <PERSON><PERSON>Footer,\r\n} from \"@/components/ui/dialog\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Check, CheckCircle2, Search, X } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Message } from \"@/types/base\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { useFriendStore } from \"@/stores/friendStore\";\r\nimport { getUserInitials } from \"@/utils/userUtils\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\n\r\ninterface ForwardMessageDialogProps {\r\n  message: Message | null;\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport default function ForwardMessageDialog({\r\n  message,\r\n  isOpen,\r\n  onOpenChange,\r\n}: ForwardMessageDialogProps) {\r\n  const [selectedRecipients, setSelectedRecipients] = useState<\r\n    Array<{ type: \"USER\" | \"GROUP\"; id: string; name: string }>\r\n  >([]);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [forwardSuccess, setForwardSuccess] = useState(false);\r\n  const [isForwarding, setIsForwarding] = useState(false);\r\n  const [activeTab, setActiveTab] = useState(\"conversations\");\r\n\r\n  const { forwardMessageToRecipients } = useChatStore();\r\n  const { conversations } = useConversationsStore();\r\n  const { friends, fetchFriends, isLoading } = useFriendStore();\r\n\r\n  // Reset state when dialog opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      setSelectedRecipients([]);\r\n      setSearchQuery(\"\");\r\n      setForwardSuccess(false);\r\n      setIsForwarding(false);\r\n      setActiveTab(\"conversations\");\r\n      fetchFriends();\r\n    }\r\n  }, [isOpen, fetchFriends]);\r\n\r\n  const handleForwardMessage = async () => {\r\n    if (!message || selectedRecipients.length === 0) return;\r\n\r\n    setIsForwarding(true);\r\n\r\n    const recipients = selectedRecipients.map((recipient) => ({\r\n      type: recipient.type,\r\n      id: recipient.id,\r\n    }));\r\n\r\n    const success = await forwardMessageToRecipients(message.id, recipients);\r\n\r\n    if (success) {\r\n      setForwardSuccess(true);\r\n      // Auto close the dialog after success\r\n      setTimeout(() => {\r\n        onOpenChange(false);\r\n      }, 2000);\r\n    } else {\r\n      setIsForwarding(false);\r\n    }\r\n  };\r\n\r\n  const toggleRecipient = (\r\n    type: \"USER\" | \"GROUP\",\r\n    id: string,\r\n    name: string,\r\n  ) => {\r\n    // Prevent selecting the original sender or group\r\n    if (message) {\r\n      // Don't allow selecting the original sender\r\n      if (message.senderId === id) {\r\n        return;\r\n      }\r\n\r\n      // Don't allow selecting the original group\r\n      if (message.groupId && message.groupId === id) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    setSelectedRecipients((prev) => {\r\n      const exists = prev.some((r) => r.id === id && r.type === type);\r\n\r\n      if (exists) {\r\n        return prev.filter((r) => !(r.id === id && r.type === type));\r\n      } else {\r\n        return [...prev, { type, id, name }];\r\n      }\r\n    });\r\n  };\r\n\r\n  // Filter conversations based on search query and exclude original sender/group\r\n  const filteredConversations = conversations.filter((conv) => {\r\n    const fullName = conv.contact.userInfo?.fullName?.toLowerCase() || \"\";\r\n    const query = searchQuery.toLowerCase();\r\n\r\n    // Exclude the original sender or group of the message\r\n    if (message) {\r\n      // For user messages, exclude the original sender\r\n      if (\r\n        message.senderId === conv.contact.id ||\r\n        message.receiverId === conv.contact.id\r\n      ) {\r\n        return false;\r\n      }\r\n\r\n      // For group messages, exclude the original group\r\n      if (message.groupId && message.groupId === conv.contact.id) {\r\n        return false;\r\n      }\r\n    }\r\n\r\n    return fullName.includes(query);\r\n  });\r\n\r\n  // Filter friends based on search query and exclude original sender\r\n  const filteredFriends = friends.filter((friend) => {\r\n    const fullName = friend.fullName?.toLowerCase() || \"\";\r\n    const query = searchQuery.toLowerCase();\r\n\r\n    // Exclude the original sender of the message\r\n    if (message && message.senderId === friend.id) {\r\n      return false;\r\n    }\r\n\r\n    return fullName.includes(query);\r\n  });\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[500px] max-h-[80vh] flex flex-col overflow-hidden\">\r\n        <DialogHeader>\r\n          <DialogTitle>Chuyển tiếp tin nhắn</DialogTitle>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex-1 overflow-hidden\">\r\n          {!forwardSuccess ? (\r\n            <>\r\n              <div className=\"border rounded-md flex items-center px-3  mb-2\">\r\n                <Search className=\"h-4 w-4 text-gray-500 mr-2\" />\r\n                <Input\r\n                  placeholder=\"Tìm kiếm người nhận...\"\r\n                  className=\"border-0 focus-visible:ring-0 shadow-none focus-visible:ring-offset-0 p-0\"\r\n                  value={searchQuery}\r\n                  onChange={(e) => setSearchQuery(e.target.value)}\r\n                />\r\n              </div>\r\n\r\n              {selectedRecipients.length > 0 && (\r\n                <div className=\"mb-4 flex flex-wrap gap-1\">\r\n                  {selectedRecipients.map((recipient) => (\r\n                    <div\r\n                      key={`${recipient.type}-${recipient.id}`}\r\n                      className=\"bg-blue-100 text-blue-800 text-xs rounded-full px-2 py-1 flex items-center gap-1\"\r\n                    >\r\n                      <span>{recipient.name}</span>\r\n                      <button\r\n                        className=\"text-blue-600 hover:text-blue-800\"\r\n                        onClick={() =>\r\n                          toggleRecipient(\r\n                            recipient.type,\r\n                            recipient.id,\r\n                            recipient.name,\r\n                          )\r\n                        }\r\n                      >\r\n                        <X className=\"h-3 w-3\" />\r\n                      </button>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n\r\n              <Tabs\r\n                value={activeTab}\r\n                onValueChange={setActiveTab}\r\n                className=\"w-full\"\r\n              >\r\n                <TabsList className=\"grid w-full grid-cols-2 mb-4\">\r\n                  <TabsTrigger value=\"conversations\">Gần đây</TabsTrigger>\r\n                  <TabsTrigger value=\"friends\">Danh sách bạn bè</TabsTrigger>\r\n                </TabsList>\r\n\r\n                <TabsContent value=\"conversations\" className=\"mt-0\">\r\n                  <div className=\"flex-1 mb-4 border rounded-md h-[300px]\">\r\n                    <ScrollArea className=\"h-full\">\r\n                      <div className=\"divide-y\">\r\n                        {filteredConversations.map((conversation) => (\r\n                          <div\r\n                            key={conversation.contact.id}\r\n                            className={`p-3 flex items-center justify-between hover:bg-gray-50 cursor-pointer ${\r\n                              selectedRecipients.some(\r\n                                (r) => r.id === conversation.contact.id,\r\n                              )\r\n                                ? \"bg-blue-50\"\r\n                                : \"\"\r\n                            }`}\r\n                            onClick={() =>\r\n                              toggleRecipient(\r\n                                conversation.type as \"USER\" | \"GROUP\",\r\n                                conversation.contact.id,\r\n                                conversation.contact.userInfo?.fullName ||\r\n                                  \"Unknown\",\r\n                              )\r\n                            }\r\n                          >\r\n                            <div className=\"flex items-center gap-3\">\r\n                              <Avatar className=\"h-10 w-10\">\r\n                                <AvatarImage\r\n                                  className=\"object-cover\"\r\n                                  src={\r\n                                    conversation.contact.userInfo\r\n                                      ?.profilePictureUrl || undefined\r\n                                  }\r\n                                  alt={\r\n                                    conversation.contact.userInfo?.fullName ||\r\n                                    \"\"\r\n                                  }\r\n                                />\r\n                                <AvatarFallback>\r\n                                  {getUserInitials(conversation.contact)}\r\n                                </AvatarFallback>\r\n                              </Avatar>\r\n                              <div>\r\n                                <p className=\"font-medium\">\r\n                                  {conversation.contact.userInfo?.fullName ||\r\n                                    \"Unknown\"}\r\n                                </p>\r\n                                <p className=\"text-xs text-gray-500\">\r\n                                  {conversation.type === \"GROUP\"\r\n                                    ? \"Nhóm\"\r\n                                    : \"Liên hệ\"}\r\n                                </p>\r\n                              </div>\r\n                            </div>\r\n                            {selectedRecipients.some(\r\n                              (r) => r.id === conversation.contact.id,\r\n                            ) && (\r\n                              <div className=\"h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white\">\r\n                                <Check className=\"h-4 w-4\" />\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        ))}\r\n\r\n                        {filteredConversations.length === 0 && (\r\n                          <div className=\"p-4 text-center text-gray-500\">\r\n                            Không tìm thấy kết quả\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    </ScrollArea>\r\n                  </div>\r\n                </TabsContent>\r\n\r\n                <TabsContent value=\"friends\" className=\"mt-0\">\r\n                  <div className=\"flex-1 mb-4 border rounded-md h-[300px]\">\r\n                    <ScrollArea className=\"h-full\">\r\n                      {isLoading.friends ? (\r\n                        <div className=\"p-4 text-center text-gray-500\">\r\n                          Đang tải danh sách bạn bè...\r\n                        </div>\r\n                      ) : (\r\n                        <div className=\"divide-y\">\r\n                          {filteredFriends.map((friend) => (\r\n                            <div\r\n                              key={friend.id}\r\n                              className={`p-3 flex items-center justify-between hover:bg-gray-50 cursor-pointer ${\r\n                                selectedRecipients.some(\r\n                                  (r) => r.id === friend.id,\r\n                                )\r\n                                  ? \"bg-blue-50\"\r\n                                  : \"\"\r\n                              }`}\r\n                              onClick={() =>\r\n                                toggleRecipient(\r\n                                  \"USER\",\r\n                                  friend.id,\r\n                                  friend.fullName || \"Unknown\",\r\n                                )\r\n                              }\r\n                            >\r\n                              <div className=\"flex items-center gap-3\">\r\n                                <Avatar className=\"h-10 w-10\">\r\n                                  <AvatarImage\r\n                                    src={friend.profilePictureUrl || \"\"}\r\n                                    alt={friend.fullName || \"\"}\r\n                                  />\r\n                                  <AvatarFallback>\r\n                                    {friend.fullName\r\n                                      ?.split(\" \")\r\n                                      .map((name) => name[0])\r\n                                      .join(\"\") || \"U\"}\r\n                                  </AvatarFallback>\r\n                                </Avatar>\r\n                                <div>\r\n                                  <p className=\"font-medium\">\r\n                                    {friend.fullName || \"Unknown\"}\r\n                                  </p>\r\n                                  <p className=\"text-xs text-gray-500\">\r\n                                    Bạn bè\r\n                                  </p>\r\n                                </div>\r\n                              </div>\r\n                              {selectedRecipients.some(\r\n                                (r) => r.id === friend.id,\r\n                              ) && (\r\n                                <div className=\"h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white\">\r\n                                  <Check className=\"h-4 w-4\" />\r\n                                </div>\r\n                              )}\r\n                            </div>\r\n                          ))}\r\n\r\n                          {filteredFriends.length === 0 && (\r\n                            <div className=\"p-4 text-center text-gray-500\">\r\n                              {searchQuery\r\n                                ? \"Không tìm thấy kết quả\"\r\n                                : \"Bạn chưa có bạn bè nào\"}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                      )}\r\n                    </ScrollArea>\r\n                  </div>\r\n                </TabsContent>\r\n              </Tabs>\r\n            </>\r\n          ) : (\r\n            <div className=\"flex-1 p-8 text-center\">\r\n              <CheckCircle2 className=\"h-12 w-12 text-green-600 mx-auto mb-4\" />\r\n              <p className=\"font-medium text-lg\">Đã chuyển tiếp thành công!</p>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {!forwardSuccess && (\r\n          <DialogFooter className=\"mt-4\">\r\n            <Button\r\n              className=\"w-full bg-blue-500 hover:bg-blue-600\"\r\n              disabled={selectedRecipients.length === 0 || isForwarding}\r\n              onClick={handleForwardMessage}\r\n            >\r\n              {isForwarding ? (\r\n                <>\r\n                  <span className=\"animate-spin mr-2\">&#8635;</span>\r\n                  Đang chuyển tiếp...\r\n                </>\r\n              ) : (\r\n                \"Chuyển tiếp\"\r\n              )}\r\n            </Button>\r\n          </DialogFooter>\r\n        )}\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;AApBA;;;;;;;;;;;;;AA4Be,SAAS,qBAAqB,EAC3C,OAAO,EACP,MAAM,EACN,YAAY,EACc;;IAC1B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEzD,EAAE;IACJ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,EAAE,0BAA0B,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAClD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;IAC9C,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD;IAE1D,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,QAAQ;gBACV,sBAAsB,EAAE;gBACxB,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;gBAChB,aAAa;gBACb;YACF;QACF;yCAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,uBAAuB;QAC3B,IAAI,CAAC,WAAW,mBAAmB,MAAM,KAAK,GAAG;QAEjD,gBAAgB;QAEhB,MAAM,aAAa,mBAAmB,GAAG,CAAC,CAAC,YAAc,CAAC;gBACxD,MAAM,UAAU,IAAI;gBACpB,IAAI,UAAU,EAAE;YAClB,CAAC;QAED,MAAM,UAAU,MAAM,2BAA2B,QAAQ,EAAE,EAAE;QAE7D,IAAI,SAAS;YACX,kBAAkB;YAClB,sCAAsC;YACtC,WAAW;gBACT,aAAa;YACf,GAAG;QACL,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,CACtB,MACA,IACA;QAEA,iDAAiD;QACjD,IAAI,SAAS;YACX,4CAA4C;YAC5C,IAAI,QAAQ,QAAQ,KAAK,IAAI;gBAC3B;YACF;YAEA,2CAA2C;YAC3C,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,KAAK,IAAI;gBAC7C;YACF;QACF;QAEA,sBAAsB,CAAC;YACrB,MAAM,SAAS,KAAK,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK;YAE1D,IAAI,QAAQ;gBACV,OAAO,KAAK,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,KAAK,MAAM,EAAE,IAAI,KAAK,IAAI;YAC5D,OAAO;gBACL,OAAO;uBAAI;oBAAM;wBAAE;wBAAM;wBAAI;oBAAK;iBAAE;YACtC;QACF;IACF;IAEA,+EAA+E;IAC/E,MAAM,wBAAwB,cAAc,MAAM,CAAC,CAAC;QAClD,MAAM,WAAW,KAAK,OAAO,CAAC,QAAQ,EAAE,UAAU,iBAAiB;QACnE,MAAM,QAAQ,YAAY,WAAW;QAErC,sDAAsD;QACtD,IAAI,SAAS;YACX,iDAAiD;YACjD,IACE,QAAQ,QAAQ,KAAK,KAAK,OAAO,CAAC,EAAE,IACpC,QAAQ,UAAU,KAAK,KAAK,OAAO,CAAC,EAAE,EACtC;gBACA,OAAO;YACT;YAEA,iDAAiD;YACjD,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,KAAK,KAAK,OAAO,CAAC,EAAE,EAAE;gBAC1D,OAAO;YACT;QACF;QAEA,OAAO,SAAS,QAAQ,CAAC;IAC3B;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC;QACtC,MAAM,WAAW,OAAO,QAAQ,EAAE,iBAAiB;QACnD,MAAM,QAAQ,YAAY,WAAW;QAErC,6CAA6C;QAC7C,IAAI,WAAW,QAAQ,QAAQ,KAAK,OAAO,EAAE,EAAE;YAC7C,OAAO;QACT;QAEA,OAAO,SAAS,QAAQ,CAAC;IAC3B;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;8BACZ,CAAC,+BACA;;0CACE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,WAAU;wCACV,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;4BAIjD,mBAAmB,MAAM,GAAG,mBAC3B,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,0BACvB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;0DAAM,UAAU,IAAI;;;;;;0DACrB,6LAAC;gDACC,WAAU;gDACV,SAAS,IACP,gBACE,UAAU,IAAI,EACd,UAAU,EAAE,EACZ,UAAU,IAAI;0DAIlB,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAdV,GAAG,UAAU,IAAI,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE;;;;;;;;;;0CAqBhD,6LAAC,mIAAA,CAAA,OAAI;gCACH,OAAO;gCACP,eAAe;gCACf,WAAU;;kDAEV,6LAAC,mIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAgB;;;;;;0DACnC,6LAAC,mIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;;;;;;;kDAG/B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAgB,WAAU;kDAC3C,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;;wDACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,6LAAC;gEAEC,WAAW,CAAC,sEAAsE,EAChF,mBAAmB,IAAI,CACrB,CAAC,IAAM,EAAE,EAAE,KAAK,aAAa,OAAO,CAAC,EAAE,IAErC,eACA,IACJ;gEACF,SAAS,IACP,gBACE,aAAa,IAAI,EACjB,aAAa,OAAO,CAAC,EAAE,EACvB,aAAa,OAAO,CAAC,QAAQ,EAAE,YAC7B;;kFAIN,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qIAAA,CAAA,SAAM;gFAAC,WAAU;;kGAChB,6LAAC,qIAAA,CAAA,cAAW;wFACV,WAAU;wFACV,KACE,aAAa,OAAO,CAAC,QAAQ,EACzB,qBAAqB;wFAE3B,KACE,aAAa,OAAO,CAAC,QAAQ,EAAE,YAC/B;;;;;;kGAGJ,6LAAC,qIAAA,CAAA,iBAAc;kGACZ,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,OAAO;;;;;;;;;;;;0FAGzC,6LAAC;;kGACC,6LAAC;wFAAE,WAAU;kGACV,aAAa,OAAO,CAAC,QAAQ,EAAE,YAC9B;;;;;;kGAEJ,6LAAC;wFAAE,WAAU;kGACV,aAAa,IAAI,KAAK,UACnB,SACA;;;;;;;;;;;;;;;;;;oEAIT,mBAAmB,IAAI,CACtB,CAAC,IAAM,EAAE,EAAE,KAAK,aAAa,OAAO,CAAC,EAAE,mBAEvC,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;;+DAlDhB,aAAa,OAAO,CAAC,EAAE;;;;;wDAwD/B,sBAAsB,MAAM,KAAK,mBAChC,6LAAC;4DAAI,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASzD,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;kDACrC,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;gDAAC,WAAU;0DACnB,UAAU,OAAO,iBAChB,6LAAC;oDAAI,WAAU;8DAAgC;;;;;yEAI/C,6LAAC;oDAAI,WAAU;;wDACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,6LAAC;gEAEC,WAAW,CAAC,sEAAsE,EAChF,mBAAmB,IAAI,CACrB,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE,IAEvB,eACA,IACJ;gEACF,SAAS,IACP,gBACE,QACA,OAAO,EAAE,EACT,OAAO,QAAQ,IAAI;;kFAIvB,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,qIAAA,CAAA,SAAM;gFAAC,WAAU;;kGAChB,6LAAC,qIAAA,CAAA,cAAW;wFACV,KAAK,OAAO,iBAAiB,IAAI;wFACjC,KAAK,OAAO,QAAQ,IAAI;;;;;;kGAE1B,6LAAC,qIAAA,CAAA,iBAAc;kGACZ,OAAO,QAAQ,EACZ,MAAM,KACP,IAAI,CAAC,OAAS,IAAI,CAAC,EAAE,EACrB,KAAK,OAAO;;;;;;;;;;;;0FAGnB,6LAAC;;kGACC,6LAAC;wFAAE,WAAU;kGACV,OAAO,QAAQ,IAAI;;;;;;kGAEtB,6LAAC;wFAAE,WAAU;kGAAwB;;;;;;;;;;;;;;;;;;oEAKxC,mBAAmB,IAAI,CACtB,CAAC,IAAM,EAAE,EAAE,KAAK,OAAO,EAAE,mBAEzB,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;;;;;;;+DA1ChB,OAAO,EAAE;;;;;wDAgDjB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;4DAAI,WAAU;sEACZ,cACG,2BACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAWtB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,wNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC;gCAAE,WAAU;0CAAsB;;;;;;;;;;;;;;;;;gBAKxC,CAAC,gCACA,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,UAAU,mBAAmB,MAAM,KAAK,KAAK;wBAC7C,SAAS;kCAER,6BACC;;8CACE,6LAAC;oCAAK,WAAU;8CAAoB;;;;;;gCAAc;;2CAIpD;;;;;;;;;;;;;;;;;;;;;;AAQhB;GA3VwB;;QAaiB,6HAAA,CAAA,eAAY;QACzB,sIAAA,CAAA,wBAAqB;QACF,+HAAA,CAAA,iBAAc;;;KAfrC", "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ReactionPicker.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Heart, X } from \"lucide-react\";\r\nimport { ReactionType } from \"@/types/base\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { EmojiStyle } from \"emoji-picker-react\";\r\nimport {\r\n  getReactionUnifiedCodes,\r\n  getReactionLabels,\r\n  getReactionTypeFromObject,\r\n  renderCenteredEmoji,\r\n  ReactionObject,\r\n} from \"@/utils/reactionUtils\";\r\n\r\ninterface ReactionPickerProps {\r\n  isCurrentUser: boolean;\r\n  userReaction?: ReactionObject | null;\r\n  onReaction: (reactionType: ReactionType) => Promise<void>;\r\n  onRemoveReaction: () => Promise<void>;\r\n}\r\n\r\nexport default function ReactionPicker({\r\n  isCurrentUser,\r\n  userReaction,\r\n  onReaction,\r\n  onRemoveReaction,\r\n}: ReactionPickerProps) {\r\n  // State to control popover open state\r\n  const [open, setOpen] = useState(false);\r\n\r\n  // Get emoji unified codes and labels\r\n  const reactionUnifiedCodes = getReactionUnifiedCodes();\r\n  const reactionLabels = getReactionLabels();\r\n\r\n  // If user has already reacted, show buttons to add more or remove reaction\r\n  if (userReaction) {\r\n    // Get the unified code for the current reaction\r\n    const reactionType = getReactionTypeFromObject(userReaction);\r\n    const unifiedCode = reactionUnifiedCodes[reactionType];\r\n\r\n    // When user has already reacted, show two buttons: add more and remove\r\n    return (\r\n      <div className=\"flex gap-1 ml-1\">\r\n        {/* Button to add more of the same reaction */}\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-5 w-5 rounded-full bg-blue-50 shadow-sm hover:bg-blue-100 p-0 flex items-center justify-center\"\r\n          onClick={() => onReaction(reactionType)}\r\n          title={`Thêm biểu cảm ${reactionLabels[reactionType]}`}\r\n        >\r\n          {renderCenteredEmoji(unifiedCode, 16, EmojiStyle.FACEBOOK)}\r\n        </Button>\r\n\r\n        {/* Button to remove reaction */}\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-5 w-5 rounded-full bg-white shadow-sm hover:bg-gray-100 p-0\"\r\n          onClick={onRemoveReaction}\r\n          title=\"Xóa biểu cảm\"\r\n        >\r\n          <X className=\"h-3 w-3 text-gray-500\" />\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // When user hasn't reacted yet, show reaction button with picker\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"h-5 w-5 rounded-full shadow-sm hover:bg-gray-100 bg-white p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ml-1\"\r\n          title=\"Thêm biểu cảm\"\r\n        >\r\n          <Heart className=\"h-3 w-3 text-gray-600\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n\r\n      <PopoverContent\r\n        className=\"w-auto p-1 rounded-full shadow-lg flex items-center gap-1 z-[999999]\"\r\n        align={isCurrentUser ? \"end\" : \"center\"}\r\n        side={isCurrentUser ? \"top\" : \"bottom\"}\r\n        sideOffset={8}\r\n      >\r\n        {Object.entries(reactionUnifiedCodes).map(\r\n          ([reactionType, unifiedCode]) => (\r\n            <button\r\n              key={reactionType}\r\n              className=\"w-8 h-8 rounded-full hover:bg-gray-100 flex items-center justify-center transition-all duration-150 hover:scale-125 hover:shadow-md group/reaction\"\r\n              onClick={() => onReaction(reactionType as ReactionType)}\r\n            >\r\n              {renderCenteredEmoji(unifiedCode, 24, EmojiStyle.FACEBOOK)}\r\n            </button>\r\n          ),\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AACA;AAKA;AACA;;;AAZA;;;;;;;AA2Be,SAAS,eAAe,EACrC,aAAa,EACb,YAAY,EACZ,UAAU,EACV,gBAAgB,EACI;;IACpB,sCAAsC;IACtC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qCAAqC;IACrC,MAAM,uBAAuB,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD;IACnD,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,oBAAiB,AAAD;IAEvC,2EAA2E;IAC3E,IAAI,cAAc;QAChB,gDAAgD;QAChD,MAAM,eAAe,CAAA,GAAA,gIAAA,CAAA,4BAAyB,AAAD,EAAE;QAC/C,MAAM,cAAc,oBAAoB,CAAC,aAAa;QAEtD,uEAAuE;QACvE,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,IAAM,WAAW;oBAC1B,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC,aAAa,EAAE;8BAErD,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI,sLAAA,CAAA,aAAU,CAAC,QAAQ;;;;;;8BAI3D,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS;oBACT,OAAM;8BAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAIrB;IAEA,iEAAiE;IACjE,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,OAAM;8BAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,WAAU;gBACV,OAAO,gBAAgB,QAAQ;gBAC/B,MAAM,gBAAgB,QAAQ;gBAC9B,YAAY;0BAEX,OAAO,OAAO,CAAC,sBAAsB,GAAG,CACvC,CAAC,CAAC,cAAc,YAAY,iBAC1B,6LAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,WAAW;kCAEzB,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,IAAI,sLAAA,CAAA,aAAU,CAAC,QAAQ;uBAJpD;;;;;;;;;;;;;;;;AAWnB;GAjFwB;KAAA", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ReactionSummary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactionType } from \"@/types/base\";\r\nimport { EmojiStyle } from \"emoji-picker-react\";\r\nimport {\r\n  getReactionUnifiedCodes,\r\n  processReactions,\r\n  renderCenteredEmoji,\r\n  ReactionObject,\r\n} from \"@/utils/reactionUtils\";\r\n\r\ninterface ReactionSummaryProps {\r\n  reactions: Array<ReactionObject>;\r\n}\r\n\r\nexport default function ReactionSummary({ reactions }: ReactionSummaryProps) {\r\n  if (!reactions || reactions.length === 0) {\r\n    return null;\r\n  }\r\n\r\n  // Process reactions using helper function\r\n  const { reactionCounts } = processReactions(reactions);\r\n\r\n  // Get emoji unified codes\r\n  const reactionUnifiedCodes = getReactionUnifiedCodes();\r\n\r\n  return (\r\n    <div className=\"flex items-center bg-white rounded-full shadow-sm px-1 py-0.5 text-xs\">\r\n      {/* Display unique reaction types with individual counts */}\r\n      {Object.entries(reactionCounts).map(([typeStr, count]) => {\r\n        // Ensure that the type string is a valid ReactionType\r\n        const type = typeStr as ReactionType;\r\n        return (\r\n          <span\r\n            key={typeStr}\r\n            className=\"mr-1.5 flex items-center gap-0.5 group\"\r\n            title={`${count} ${typeStr.toLowerCase()}`}\r\n          >\r\n            {renderCenteredEmoji(\r\n              reactionUnifiedCodes[type],\r\n              14,\r\n              EmojiStyle.FACEBOOK,\r\n            )}\r\n            <span className=\"text-gray-600 font-medium\">{count}</span>\r\n          </span>\r\n        );\r\n      })}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAee,SAAS,gBAAgB,EAAE,SAAS,EAAwB;IACzE,IAAI,CAAC,aAAa,UAAU,MAAM,KAAK,GAAG;QACxC,OAAO;IACT;IAEA,0CAA0C;IAC1C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAE;IAE5C,0BAA0B;IAC1B,MAAM,uBAAuB,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD;IAEnD,qBACE,6LAAC;QAAI,WAAU;kBAEZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,SAAS,MAAM;YACnD,sDAAsD;YACtD,MAAM,OAAO;YACb,qBACE,6LAAC;gBAEC,WAAU;gBACV,OAAO,GAAG,MAAM,CAAC,EAAE,QAAQ,WAAW,IAAI;;oBAEzC,CAAA,GAAA,gIAAA,CAAA,sBAAmB,AAAD,EACjB,oBAAoB,CAAC,KAAK,EAC1B,IACA,sLAAA,CAAA,aAAU,CAAC,QAAQ;kCAErB,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;eATxC;;;;;QAYX;;;;;;AAGN;KAlCwB", "debugId": null}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/AudioVisualizer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect, useRef } from \"react\";\nimport { Music, Play, Pause, Download } from \"lucide-react\";\nimport { Button } from \"@/components/ui/button\";\n\ninterface AudioVisualizerProps {\n  url: string;\n  fileName: string;\n  onDownload?: () => void;\n  compact?: boolean;\n}\n\nexport default function AudioVisualizer({\n  url,\n  fileName,\n  onDownload,\n  compact = false,\n}: AudioVisualizerProps) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [duration, setDuration] = useState(0);\n  const [currentTime, setCurrentTime] = useState(0);\n  const audioRef = useRef<HTMLAudioElement | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [loadError, setLoadError] = useState<string | null>(null);\n\n  // Create audio element for playback control\n  useEffect(() => {\n    let isMounted = true;\n    let loadAttempts = 0;\n    const maxAttempts = 3;\n\n    // Set initial loading state\n    setIsLoading(true);\n    setLoadError(null);\n\n    // Create audio element\n    const audio = new Audio();\n\n    // Set up event listeners\n    // We no longer need these functions since we don't estimate duration\n\n    // Try to extract duration from filename\n    const extractDurationFromFileName = () => {\n      try {\n        // Check for duration in filename format: audio_message_TIMESTAMP_duration_X.mp3\n        const durationMatch = fileName.match(/_duration_(\\d+)/);\n        if (durationMatch && durationMatch[1]) {\n          const extractedDuration = parseFloat(durationMatch[1]);\n          if (isFinite(extractedDuration) && extractedDuration > 0) {\n            console.log(\"Extracted duration from filename:\", extractedDuration);\n            return extractedDuration;\n          }\n        }\n        return 0;\n      } catch (error) {\n        console.error(\"Error extracting duration from filename:\", error);\n        return 0;\n      }\n    };\n\n    const handleLoadedMetadata = () => {\n      if (isMounted) {\n        console.log(\"Audio metadata loaded, duration:\", audio.duration);\n        if (isFinite(audio.duration) && audio.duration > 0) {\n          // If we have a valid duration from the audio element, use it\n          setDuration(audio.duration);\n        } else {\n          // Try to extract duration from filename\n          const extractedDuration = extractDurationFromFileName();\n          if (extractedDuration > 0) {\n            setDuration(extractedDuration);\n          } else {\n            // If still no valid duration, set it to 0 (will hide progress bar)\n            console.log(\"Invalid duration, hiding progress bar\");\n            setDuration(0);\n          }\n        }\n        setIsLoading(false);\n      }\n    };\n\n    const handleTimeUpdate = () => {\n      if (isMounted) {\n        setCurrentTime(audio.currentTime);\n      }\n    };\n\n    const handleEnded = () => {\n      if (isMounted) {\n        setIsPlaying(false);\n        setCurrentTime(0);\n      }\n    };\n\n    const handleError = (event?: Event) => {\n      // Xử lý an toàn khi truy cập thông tin lỗi\n      let errorInfo: {\n        message?: string;\n        code?: number | string;\n        error?: string;\n      } = {};\n\n      try {\n        // Kiểm tra xem audio.error có tồn tại không\n        if (audio && audio.error) {\n          errorInfo = {\n            code: audio.error.code || \"unknown\",\n            message: audio.error.message || \"Unknown audio error\",\n          };\n        } else {\n          errorInfo = { message: \"Audio error object is null or undefined\" };\n        }\n      } catch (err) {\n        errorInfo = {\n          message: \"Error accessing audio.error\",\n          error: String(err),\n        };\n      }\n\n      // Create a comprehensive error message with all details\n      const errorMessage = `Audio loading error: ${errorInfo.message || \"Unknown error\"}`;\n      const errorDetails = {\n        errorCode: errorInfo.code,\n        url: url,\n        fileName: fileName,\n        eventType: event ? event.type : \"unknown\",\n        audioState: audio\n          ? {\n              paused: audio.paused,\n              ended: audio.ended,\n              networkState: audio.networkState,\n              readyState: audio.readyState,\n              src: audio.src,\n            }\n          : \"audio object unavailable\",\n        loadAttempts: loadAttempts + 1,\n        maxAttempts: maxAttempts,\n      };\n\n      // Log with proper error details - avoid empty object logging\n      console.error(errorMessage, errorDetails);\n\n      // Implement exponential backoff for retries\n      if (loadAttempts < maxAttempts) {\n        loadAttempts++;\n        // Exponential backoff: 1s, 2s, 4s...\n        const delay = Math.pow(2, loadAttempts - 1) * 1000;\n        console.log(\n          `Retry attempt ${loadAttempts}/${maxAttempts} in ${delay}ms`,\n        );\n\n        setTimeout(() => {\n          if (isMounted) {\n            // Try with a different approach on subsequent attempts\n            if (loadAttempts > 1) {\n              // On second attempt, try with a cache-busting parameter\n              audio.src = `${url}${url.includes(\"?\") ? \"&\" : \"?\"}_cb=${Date.now()}`;\n            } else {\n              audio.src = url;\n            }\n            audio.load();\n          }\n        }, delay);\n      } else {\n        // If we've tried multiple times and failed, try to extract duration from filename\n        const extractedDuration = extractDurationFromFileName();\n        if (extractedDuration > 0) {\n          console.log(\n            \"Using duration from filename after load failure:\",\n            extractedDuration,\n          );\n          setDuration(extractedDuration);\n        } else {\n          // If still no valid duration, set it to 0 (will hide progress bar)\n          console.log(\n            \"Failed to load audio after multiple attempts, hiding progress bar\",\n          );\n          setDuration(0);\n        }\n\n        // Set a user-friendly error message\n        if (isMounted) {\n          setLoadError(\n            \"Không thể tải tệp âm thanh. Vui lòng tải xuống để nghe.\",\n          );\n        }\n\n        // Create a fallback audio element as a last resort\n        try {\n          // Try with a different audio constructor approach\n          const fallbackAudio = new window.Audio(url);\n          audioRef.current = fallbackAudio;\n\n          // Set up minimal event listeners for the fallback\n          fallbackAudio.addEventListener(\"loadedmetadata\", () => {\n            if (\n              isMounted &&\n              isFinite(fallbackAudio.duration) &&\n              fallbackAudio.duration > 0\n            ) {\n              setDuration(fallbackAudio.duration);\n            }\n          });\n\n          fallbackAudio.addEventListener(\"timeupdate\", () => {\n            if (isMounted) {\n              setCurrentTime(fallbackAudio.currentTime);\n            }\n          });\n\n          fallbackAudio.addEventListener(\"ended\", () => {\n            if (isMounted) {\n              setIsPlaying(false);\n              setCurrentTime(0);\n            }\n          });\n\n          // Set a specific error for the fallback attempt\n          fallbackAudio.addEventListener(\"error\", (fallbackEvent) => {\n            if (isMounted) {\n              console.error(\"Fallback audio also failed to load\", {\n                url: url,\n                fileName: fileName,\n                eventType: fallbackEvent.type,\n                fallbackError: fallbackAudio.error,\n              });\n              setLoadError(\"Không thể tải tệp âm thanh. Vui lòng thử lại sau.\");\n            }\n          });\n        } catch (fallbackError) {\n          console.error(\"Fallback audio creation failed:\", fallbackError);\n          if (isMounted) {\n            setLoadError(\"Không thể tải tệp âm thanh. Vui lòng thử lại sau.\");\n          }\n        }\n\n        setIsLoading(false);\n      }\n    };\n\n    // Add event listeners\n    audio.addEventListener(\"loadedmetadata\", handleLoadedMetadata);\n    audio.addEventListener(\"timeupdate\", handleTimeUpdate);\n    audio.addEventListener(\"ended\", handleEnded);\n    audio.addEventListener(\"error\", handleError);\n\n    // Tạo các hàm xử lý riêng biệt để có thể gỡ bỏ đăng ký sau này\n    const handleStalled = (event: Event) => {\n      console.warn(\"Audio playback stalled\", {\n        url: url,\n        fileName: fileName,\n        eventType: event.type,\n      });\n      handleError(event);\n    };\n\n    const handleAbort = (event: Event) => {\n      console.warn(\"Audio loading aborted\", {\n        url: url,\n        fileName: fileName,\n        eventType: event.type,\n      });\n      handleError(event);\n    };\n\n    // Add additional error listeners for more comprehensive error catching\n    audio.addEventListener(\"stalled\", handleStalled);\n    audio.addEventListener(\"abort\", handleAbort);\n\n    // Set audio source and load\n    audio.preload = \"metadata\";\n\n    // Add a cache-busting parameter to the URL to avoid browser caching issues\n    const cacheBustUrl = url.includes(\"?\")\n      ? `${url}&_cb=${Date.now()}`\n      : `${url}?_cb=${Date.now()}`;\n\n    audio.src = cacheBustUrl;\n    audio.load();\n\n    // Store reference\n    audioRef.current = audio;\n\n    // Cleanup function\n    return () => {\n      isMounted = false;\n\n      try {\n        // Remove event listeners\n        audio.removeEventListener(\"loadedmetadata\", handleLoadedMetadata);\n        audio.removeEventListener(\"timeupdate\", handleTimeUpdate);\n        audio.removeEventListener(\"ended\", handleEnded);\n        audio.removeEventListener(\"error\", handleError);\n        audio.removeEventListener(\"stalled\", handleStalled);\n        audio.removeEventListener(\"abort\", handleAbort);\n      } catch (cleanupError) {\n        console.error(\"Error during audio cleanup:\", cleanupError);\n      }\n\n      // Stop playback and clean up resources\n      audio.pause();\n      audio.src = \"\";\n      audioRef.current = null;\n    };\n  }, [url, fileName]);\n\n  // Progress bar component with click to seek functionality\n  const renderProgressBar = () => {\n    // If we have a load error, show error message with download option\n    if (loadError) {\n      return (\n        <div className=\"w-full flex flex-col items-center justify-center gap-1\">\n          <div className=\"text-xs text-red-500\">{loadError}</div>\n          <button\n            onClick={(e) => {\n              e.stopPropagation();\n              handleDownload();\n            }}\n            className=\"text-xs text-blue-500 hover:underline\"\n          >\n            Tải xuống\n          </button>\n        </div>\n      );\n    }\n\n    // If duration is 0 or invalid, show only sound wave visualization\n    if (duration <= 0) {\n      return (\n        <div className=\"w-full h-3 flex items-center justify-center\">\n          {isPlaying ? (\n            <div className=\"flex items-end space-x-1 h-3\">\n              <div className=\"w-1 h-2 bg-blue-500 rounded-full animate-sound-wave-1\"></div>\n              <div className=\"w-1 h-3 bg-blue-500 rounded-full animate-sound-wave-2\"></div>\n              <div className=\"w-1 h-1.5 bg-blue-500 rounded-full animate-sound-wave-3\"></div>\n              <div className=\"w-1 h-2.5 bg-blue-500 rounded-full animate-sound-wave-2\"></div>\n              <div className=\"w-1 h-2 bg-blue-500 rounded-full animate-sound-wave-1\"></div>\n            </div>\n          ) : (\n            <div className=\"h-3 w-full bg-gray-100 rounded-full\"></div>\n          )}\n        </div>\n      );\n    }\n\n    // Calculate progress percentage safely\n    const progress = Math.min(100, Math.max(0, (currentTime / duration) * 100));\n\n    const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {\n      if (!audioRef.current) return;\n\n      // Calculate click position relative to the progress bar width\n      const progressBar = e.currentTarget;\n      const rect = progressBar.getBoundingClientRect();\n      const clickPosition = (e.clientX - rect.left) / rect.width;\n\n      // Set the audio current time based on click position\n      const newTime = clickPosition * duration;\n      audioRef.current.currentTime = newTime;\n      setCurrentTime(newTime);\n    };\n\n    return (\n      <div className=\"w-full\">\n        <div\n          className=\"w-full h-3 bg-gray-200 rounded-full overflow-hidden cursor-pointer\"\n          onClick={handleProgressBarClick}\n        >\n          <div\n            className=\"h-full bg-blue-500 transition-all duration-100\"\n            style={{ width: `${progress}%` }}\n          ></div>\n        </div>\n      </div>\n    );\n  };\n\n  const togglePlayPause = () => {\n    if (!audioRef.current) return;\n\n    if (isPlaying) {\n      audioRef.current.pause();\n      setIsPlaying(false);\n    } else {\n      // Reset to beginning if we're at the end\n      if (currentTime >= duration - 0.5) {\n        audioRef.current.currentTime = 0;\n        setCurrentTime(0);\n      }\n\n      // Use promise to handle play() properly\n      audioRef.current\n        .play()\n        .then(() => {\n          setIsPlaying(true);\n        })\n        .catch((error) => {\n          console.error(\"Error playing audio:\", {\n            error: error,\n            errorName: error.name,\n            errorMessage: error.message,\n            url: url,\n            fileName: fileName,\n          });\n\n          // Check if it's a user interaction error (common in browsers)\n          if (error.name === \"NotAllowedError\") {\n            console.warn(\"Audio playback requires user interaction first\", {\n              url: url,\n              fileName: fileName,\n            });\n            // We could show a UI message here if needed\n          }\n\n          // Try to reload the audio if play fails\n          try {\n            audioRef.current?.load();\n            // Try playing again after a short delay\n            setTimeout(() => {\n              audioRef.current?.play().catch((e) => {\n                console.error(\"Second play attempt failed:\", {\n                  error: e,\n                  url: url,\n                  fileName: fileName,\n                });\n              });\n            }, 500);\n          } catch (reloadError) {\n            console.error(\"Error reloading audio:\", {\n              error: reloadError,\n              url: url,\n              fileName: fileName,\n            });\n          }\n        });\n    }\n  };\n\n  const formatTime = (time: number) => {\n    if (!isFinite(time) || time < 0) return \"0:00\";\n    const minutes = Math.floor(time / 60);\n    const seconds = Math.floor(time % 60);\n    return `${minutes}:${seconds < 10 ? \"0\" : \"\"}${seconds}`;\n  };\n\n  const handleDownload = (e?: React.MouseEvent) => {\n    if (e) {\n      e.preventDefault();\n      e.stopPropagation();\n    }\n\n    if (onDownload) {\n      onDownload();\n    } else {\n      try {\n        // Tạo một thẻ a ẩn để tải file\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = fileName || \"audio-file\";\n        a.style.display = \"none\";\n        document.body.appendChild(a);\n        a.click();\n        setTimeout(() => {\n          document.body.removeChild(a);\n        }, 100);\n      } catch (error) {\n        console.error(\"Error downloading file:\", {\n          error: error,\n          url: url,\n          fileName: fileName,\n        });\n        // Fallback: mở file trong tab mới\n        window.open(url, \"_blank\");\n      }\n    }\n  };\n\n  if (compact) {\n    return (\n      <div className=\"flex items-center w-full gap-2 p-2 bg-gray-50 rounded-md\">\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-8 w-8 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 flex-shrink-0\"\n          onClick={(e) => {\n            e.stopPropagation();\n            if (loadError) {\n              handleDownload();\n            } else {\n              togglePlayPause();\n            }\n          }}\n          disabled={isLoading}\n        >\n          {loadError ? (\n            <Download className=\"h-4 w-4\" />\n          ) : isPlaying ? (\n            <Pause className=\"h-4 w-4\" />\n          ) : (\n            <Play className=\"h-4 w-4\" />\n          )}\n        </Button>\n\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"w-full\">\n            {/* Don't use src directly here since we're managing it in the useEffect */}\n            <audio\n              ref={audioRef}\n              className=\"hidden\"\n              preload=\"metadata\"\n              onPlay={() => setIsPlaying(true)}\n              onPause={() => setIsPlaying(false)}\n            />\n            {renderProgressBar()}\n            <div className=\"flex justify-between mt-1 text-xs text-gray-500\">\n              <span>{formatTime(currentTime)}</span>\n              {duration > 0 ? (\n                <span>{formatTime(duration)}</span>\n              ) : (\n                <span className=\"text-blue-500 font-medium\">\n                  {isPlaying ? \"Đang phát\" : \"Âm thanh\"}\n                </span>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"h-8 w-8 rounded-full hover:bg-gray-200 flex-shrink-0\"\n          onClick={(e) => {\n            e.stopPropagation();\n            handleDownload(e);\n          }}\n        >\n          <Download className=\"h-4 w-4\" />\n        </Button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"w-full max-w-md\">\n      <div className=\"flex items-center mb-2\">\n        <Music className=\"h-5 w-5 text-blue-500 mr-2\" />\n        <div className=\"text-sm font-medium truncate flex-1\">{fileName}</div>\n        <div className=\"text-xs text-gray-500\">\n          {formatTime(currentTime)} / {formatTime(duration)}\n        </div>\n      </div>\n\n      <div className=\"bg-gray-50 p-4 rounded-lg\">\n        <div className=\"flex justify-center mb-4\">\n          <div className=\"w-full\">\n            {/* Don't use src directly here since we're managing it in the useEffect */}\n            <audio\n              ref={audioRef}\n              className=\"hidden\"\n              preload=\"metadata\"\n              onPlay={() => setIsPlaying(true)}\n              onPause={() => setIsPlaying(false)}\n            />\n            {renderProgressBar()}\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-10 w-10 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700\"\n            onClick={(e) => {\n              if (loadError) {\n                handleDownload(e);\n              } else {\n                togglePlayPause();\n              }\n            }}\n            disabled={isLoading}\n          >\n            {loadError ? (\n              <Download className=\"h-5 w-5\" />\n            ) : isPlaying ? (\n              <Pause className=\"h-5 w-5\" />\n            ) : (\n              <Play className=\"h-5 w-5\" />\n            )}\n          </Button>\n\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"h-8 w-8 rounded-full hover:bg-gray-200\"\n            onClick={(e) => handleDownload(e)}\n          >\n            <Download className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAae,SAAS,gBAAgB,EACtC,GAAG,EACH,QAAQ,EACR,UAAU,EACV,UAAU,KAAK,EACM;;IACrB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,YAAY;YAChB,IAAI,eAAe;YACnB,MAAM,cAAc;YAEpB,4BAA4B;YAC5B,aAAa;YACb,aAAa;YAEb,uBAAuB;YACvB,MAAM,QAAQ,IAAI;YAElB,yBAAyB;YACzB,qEAAqE;YAErE,wCAAwC;YACxC,MAAM;yEAA8B;oBAClC,IAAI;wBACF,gFAAgF;wBAChF,MAAM,gBAAgB,SAAS,KAAK,CAAC;wBACrC,IAAI,iBAAiB,aAAa,CAAC,EAAE,EAAE;4BACrC,MAAM,oBAAoB,WAAW,aAAa,CAAC,EAAE;4BACrD,IAAI,SAAS,sBAAsB,oBAAoB,GAAG;gCACxD,QAAQ,GAAG,CAAC,qCAAqC;gCACjD,OAAO;4BACT;wBACF;wBACA,OAAO;oBACT,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4CAA4C;wBAC1D,OAAO;oBACT;gBACF;;YAEA,MAAM;kEAAuB;oBAC3B,IAAI,WAAW;wBACb,QAAQ,GAAG,CAAC,oCAAoC,MAAM,QAAQ;wBAC9D,IAAI,SAAS,MAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,GAAG;4BAClD,6DAA6D;4BAC7D,YAAY,MAAM,QAAQ;wBAC5B,OAAO;4BACL,wCAAwC;4BACxC,MAAM,oBAAoB;4BAC1B,IAAI,oBAAoB,GAAG;gCACzB,YAAY;4BACd,OAAO;gCACL,mEAAmE;gCACnE,QAAQ,GAAG,CAAC;gCACZ,YAAY;4BACd;wBACF;wBACA,aAAa;oBACf;gBACF;;YAEA,MAAM;8DAAmB;oBACvB,IAAI,WAAW;wBACb,eAAe,MAAM,WAAW;oBAClC;gBACF;;YAEA,MAAM;yDAAc;oBAClB,IAAI,WAAW;wBACb,aAAa;wBACb,eAAe;oBACjB;gBACF;;YAEA,MAAM;yDAAc,CAAC;oBACnB,2CAA2C;oBAC3C,IAAI,YAIA,CAAC;oBAEL,IAAI;wBACF,4CAA4C;wBAC5C,IAAI,SAAS,MAAM,KAAK,EAAE;4BACxB,YAAY;gCACV,MAAM,MAAM,KAAK,CAAC,IAAI,IAAI;gCAC1B,SAAS,MAAM,KAAK,CAAC,OAAO,IAAI;4BAClC;wBACF,OAAO;4BACL,YAAY;gCAAE,SAAS;4BAA0C;wBACnE;oBACF,EAAE,OAAO,KAAK;wBACZ,YAAY;4BACV,SAAS;4BACT,OAAO,OAAO;wBAChB;oBACF;oBAEA,wDAAwD;oBACxD,MAAM,eAAe,CAAC,qBAAqB,EAAE,UAAU,OAAO,IAAI,iBAAiB;oBACnF,MAAM,eAAe;wBACnB,WAAW,UAAU,IAAI;wBACzB,KAAK;wBACL,UAAU;wBACV,WAAW,QAAQ,MAAM,IAAI,GAAG;wBAChC,YAAY,QACR;4BACE,QAAQ,MAAM,MAAM;4BACpB,OAAO,MAAM,KAAK;4BAClB,cAAc,MAAM,YAAY;4BAChC,YAAY,MAAM,UAAU;4BAC5B,KAAK,MAAM,GAAG;wBAChB,IACA;wBACJ,cAAc,eAAe;wBAC7B,aAAa;oBACf;oBAEA,6DAA6D;oBAC7D,QAAQ,KAAK,CAAC,cAAc;oBAE5B,4CAA4C;oBAC5C,IAAI,eAAe,aAAa;wBAC9B;wBACA,qCAAqC;wBACrC,MAAM,QAAQ,KAAK,GAAG,CAAC,GAAG,eAAe,KAAK;wBAC9C,QAAQ,GAAG,CACT,CAAC,cAAc,EAAE,aAAa,CAAC,EAAE,YAAY,IAAI,EAAE,MAAM,EAAE,CAAC;wBAG9D;qEAAW;gCACT,IAAI,WAAW;oCACb,uDAAuD;oCACvD,IAAI,eAAe,GAAG;wCACpB,wDAAwD;wCACxD,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,GAAG,IAAI;oCACvE,OAAO;wCACL,MAAM,GAAG,GAAG;oCACd;oCACA,MAAM,IAAI;gCACZ;4BACF;oEAAG;oBACL,OAAO;wBACL,kFAAkF;wBAClF,MAAM,oBAAoB;wBAC1B,IAAI,oBAAoB,GAAG;4BACzB,QAAQ,GAAG,CACT,oDACA;4BAEF,YAAY;wBACd,OAAO;4BACL,mEAAmE;4BACnE,QAAQ,GAAG,CACT;4BAEF,YAAY;wBACd;wBAEA,oCAAoC;wBACpC,IAAI,WAAW;4BACb,aACE;wBAEJ;wBAEA,mDAAmD;wBACnD,IAAI;4BACF,kDAAkD;4BAClD,MAAM,gBAAgB,IAAI,OAAO,KAAK,CAAC;4BACvC,SAAS,OAAO,GAAG;4BAEnB,kDAAkD;4BAClD,cAAc,gBAAgB,CAAC;yEAAkB;oCAC/C,IACE,aACA,SAAS,cAAc,QAAQ,KAC/B,cAAc,QAAQ,GAAG,GACzB;wCACA,YAAY,cAAc,QAAQ;oCACpC;gCACF;;4BAEA,cAAc,gBAAgB,CAAC;yEAAc;oCAC3C,IAAI,WAAW;wCACb,eAAe,cAAc,WAAW;oCAC1C;gCACF;;4BAEA,cAAc,gBAAgB,CAAC;yEAAS;oCACtC,IAAI,WAAW;wCACb,aAAa;wCACb,eAAe;oCACjB;gCACF;;4BAEA,gDAAgD;4BAChD,cAAc,gBAAgB,CAAC;yEAAS,CAAC;oCACvC,IAAI,WAAW;wCACb,QAAQ,KAAK,CAAC,sCAAsC;4CAClD,KAAK;4CACL,UAAU;4CACV,WAAW,cAAc,IAAI;4CAC7B,eAAe,cAAc,KAAK;wCACpC;wCACA,aAAa;oCACf;gCACF;;wBACF,EAAE,OAAO,eAAe;4BACtB,QAAQ,KAAK,CAAC,mCAAmC;4BACjD,IAAI,WAAW;gCACb,aAAa;4BACf;wBACF;wBAEA,aAAa;oBACf;gBACF;;YAEA,sBAAsB;YACtB,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAEhC,+DAA+D;YAC/D,MAAM;2DAAgB,CAAC;oBACrB,QAAQ,IAAI,CAAC,0BAA0B;wBACrC,KAAK;wBACL,UAAU;wBACV,WAAW,MAAM,IAAI;oBACvB;oBACA,YAAY;gBACd;;YAEA,MAAM;yDAAc,CAAC;oBACnB,QAAQ,IAAI,CAAC,yBAAyB;wBACpC,KAAK;wBACL,UAAU;wBACV,WAAW,MAAM,IAAI;oBACvB;oBACA,YAAY;gBACd;;YAEA,uEAAuE;YACvE,MAAM,gBAAgB,CAAC,WAAW;YAClC,MAAM,gBAAgB,CAAC,SAAS;YAEhC,4BAA4B;YAC5B,MAAM,OAAO,GAAG;YAEhB,2EAA2E;YAC3E,MAAM,eAAe,IAAI,QAAQ,CAAC,OAC9B,GAAG,IAAI,KAAK,EAAE,KAAK,GAAG,IAAI,GAC1B,GAAG,IAAI,KAAK,EAAE,KAAK,GAAG,IAAI;YAE9B,MAAM,GAAG,GAAG;YACZ,MAAM,IAAI;YAEV,kBAAkB;YAClB,SAAS,OAAO,GAAG;YAEnB,mBAAmB;YACnB;6CAAO;oBACL,YAAY;oBAEZ,IAAI;wBACF,yBAAyB;wBACzB,MAAM,mBAAmB,CAAC,kBAAkB;wBAC5C,MAAM,mBAAmB,CAAC,cAAc;wBACxC,MAAM,mBAAmB,CAAC,SAAS;wBACnC,MAAM,mBAAmB,CAAC,SAAS;wBACnC,MAAM,mBAAmB,CAAC,WAAW;wBACrC,MAAM,mBAAmB,CAAC,SAAS;oBACrC,EAAE,OAAO,cAAc;wBACrB,QAAQ,KAAK,CAAC,+BAA+B;oBAC/C;oBAEA,uCAAuC;oBACvC,MAAM,KAAK;oBACX,MAAM,GAAG,GAAG;oBACZ,SAAS,OAAO,GAAG;gBACrB;;QACF;oCAAG;QAAC;QAAK;KAAS;IAElB,0DAA0D;IAC1D,MAAM,oBAAoB;QACxB,mEAAmE;QACnE,IAAI,WAAW;YACb,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;kCACvC,6LAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB;wBACF;wBACA,WAAU;kCACX;;;;;;;;;;;;QAKP;QAEA,kEAAkE;QAClE,IAAI,YAAY,GAAG;YACjB,qBACE,6LAAC;gBAAI,WAAU;0BACZ,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;yCAGjB,6LAAC;oBAAI,WAAU;;;;;;;;;;;QAIvB;QAEA,uCAAuC;QACvC,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,AAAC,cAAc,WAAY;QAEtE,MAAM,yBAAyB,CAAC;YAC9B,IAAI,CAAC,SAAS,OAAO,EAAE;YAEvB,8DAA8D;YAC9D,MAAM,cAAc,EAAE,aAAa;YACnC,MAAM,OAAO,YAAY,qBAAqB;YAC9C,MAAM,gBAAgB,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;YAE1D,qDAAqD;YACrD,MAAM,UAAU,gBAAgB;YAChC,SAAS,OAAO,CAAC,WAAW,GAAG;YAC/B,eAAe;QACjB;QAEA,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;IAKzC;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,OAAO,EAAE;QAEvB,IAAI,WAAW;YACb,SAAS,OAAO,CAAC,KAAK;YACtB,aAAa;QACf,OAAO;YACL,yCAAyC;YACzC,IAAI,eAAe,WAAW,KAAK;gBACjC,SAAS,OAAO,CAAC,WAAW,GAAG;gBAC/B,eAAe;YACjB;YAEA,wCAAwC;YACxC,SAAS,OAAO,CACb,IAAI,GACJ,IAAI,CAAC;gBACJ,aAAa;YACf,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,wBAAwB;oBACpC,OAAO;oBACP,WAAW,MAAM,IAAI;oBACrB,cAAc,MAAM,OAAO;oBAC3B,KAAK;oBACL,UAAU;gBACZ;gBAEA,8DAA8D;gBAC9D,IAAI,MAAM,IAAI,KAAK,mBAAmB;oBACpC,QAAQ,IAAI,CAAC,kDAAkD;wBAC7D,KAAK;wBACL,UAAU;oBACZ;gBACA,4CAA4C;gBAC9C;gBAEA,wCAAwC;gBACxC,IAAI;oBACF,SAAS,OAAO,EAAE;oBAClB,wCAAwC;oBACxC,WAAW;wBACT,SAAS,OAAO,EAAE,OAAO,MAAM,CAAC;4BAC9B,QAAQ,KAAK,CAAC,+BAA+B;gCAC3C,OAAO;gCACP,KAAK;gCACL,UAAU;4BACZ;wBACF;oBACF,GAAG;gBACL,EAAE,OAAO,aAAa;oBACpB,QAAQ,KAAK,CAAC,0BAA0B;wBACtC,OAAO;wBACP,KAAK;wBACL,UAAU;oBACZ;gBACF;YACF;QACJ;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,SAAS,OAAO,GAAG,OAAO;QACxC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,OAAO,GAAG,QAAQ,CAAC,EAAE,UAAU,KAAK,MAAM,KAAK,SAAS;IAC1D;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,GAAG;YACL,EAAE,cAAc;YAChB,EAAE,eAAe;QACnB;QAEA,IAAI,YAAY;YACd;QACF,OAAO;YACL,IAAI;gBACF,+BAA+B;gBAC/B,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,YAAY;gBACzB,EAAE,KAAK,CAAC,OAAO,GAAG;gBAClB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,WAAW;oBACT,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B,GAAG;YACL,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;oBACvC,OAAO;oBACP,KAAK;oBACL,UAAU;gBACZ;gBACA,kCAAkC;gBAClC,OAAO,IAAI,CAAC,KAAK;YACnB;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,IAAI,WAAW;4BACb;wBACF,OAAO;4BACL;wBACF;oBACF;oBACA,UAAU;8BAET,0BACC,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;+BAClB,0BACF,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;6CAEjB,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;;;;;;8BAIpB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCACC,KAAK;gCACL,WAAU;gCACV,SAAQ;gCACR,QAAQ,IAAM,aAAa;gCAC3B,SAAS,IAAM,aAAa;;;;;;4BAE7B;0CACD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAM,WAAW;;;;;;oCACjB,WAAW,kBACV,6LAAC;kDAAM,WAAW;;;;;6DAElB,6LAAC;wCAAK,WAAU;kDACb,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;8BAOrC,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;oBACV,SAAS,CAAC;wBACR,EAAE,eAAe;wBACjB,eAAe;oBACjB;8BAEA,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAI5B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;wBAAI,WAAU;kCAAuC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;;4BACZ,WAAW;4BAAa;4BAAI,WAAW;;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,KAAK;oCACL,WAAU;oCACV,SAAQ;oCACR,QAAQ,IAAM,aAAa;oCAC3B,SAAS,IAAM,aAAa;;;;;;gCAE7B;;;;;;;;;;;;kCAIL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC;oCACR,IAAI,WAAW;wCACb,eAAe;oCACjB,OAAO;wCACL;oCACF;gCACF;gCACA,UAAU;0CAET,0BACC,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;2CAClB,0BACF,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;yDAEjB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,CAAC,IAAM,eAAe;0CAE/B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhC;GA9kBwB;KAAA", "debugId": null}}, {"offset": {"line": 3315, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/AudioRecorder.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { Mic, Square, Send, Trash2, Play, Pause } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface AudioRecorderProps {\r\n  onSend: (audioBlob: Blob, duration?: number) => void;\r\n  onCancel: () => void;\r\n}\r\n\r\nexport default function AudioRecorder({\r\n  onSend,\r\n  onCancel,\r\n}: AudioRecorderProps) {\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [recordingTime, setRecordingTime] = useState(0);\r\n  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  const [playbackTime, setPlaybackTime] = useState(0);\r\n  const [audioDuration, setAudioDuration] = useState(0);\r\n\r\n  const mediaRecorderRef = useRef<MediaRecorder | null>(null);\r\n  const audioChunksRef = useRef<Blob[]>([]);\r\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const audioRef = useRef<HTMLAudioElement | null>(null);\r\n\r\n  // Start recording\r\n  const startRecording = async () => {\r\n    try {\r\n      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });\r\n      const mediaRecorder = new MediaRecorder(stream);\r\n      mediaRecorderRef.current = mediaRecorder;\r\n      audioChunksRef.current = [];\r\n\r\n      mediaRecorder.ondataavailable = (event) => {\r\n        if (event.data.size > 0) {\r\n          audioChunksRef.current.push(event.data);\r\n        }\r\n      };\r\n\r\n      mediaRecorder.onstop = () => {\r\n        // Use correct MIME type for browser compatibility\r\n        // Most browsers support audio/webm or audio/wav better than audio/mpeg for recorded audio\r\n        // Try different formats in order of compatibility\r\n        let audioBlob;\r\n\r\n        // First try with audio/webm (most compatible with Chrome/Firefox)\r\n        try {\r\n          audioBlob = new Blob(audioChunksRef.current, { type: \"audio/webm\" });\r\n          console.log(\"Recording stopped, blob created with type audio/webm\");\r\n        } catch (error) {\r\n          console.error(\"Error creating audio/webm blob:\", error);\r\n          // Fallback to audio/wav\r\n          try {\r\n            audioBlob = new Blob(audioChunksRef.current, { type: \"audio/wav\" });\r\n            console.log(\"Fallback: blob created with type audio/wav\");\r\n          } catch (error2) {\r\n            console.error(\"Error creating audio/wav blob:\", error2);\r\n            // Last resort: use generic audio type\r\n            audioBlob = new Blob(audioChunksRef.current, {\r\n              type: \"audio/mpeg\",\r\n            });\r\n            console.log(\"Last resort: blob created with type audio/mpeg\");\r\n          }\r\n        }\r\n\r\n        // Set the blob to state immediately with the recording time as duration\r\n        setAudioBlob(audioBlob);\r\n        setAudioDuration(recordingTime || 1); // Use recording time as a reliable duration\r\n      };\r\n\r\n      mediaRecorder.start();\r\n      setIsRecording(true);\r\n      setRecordingTime(0);\r\n\r\n      // Start timer\r\n      timerRef.current = setInterval(() => {\r\n        setRecordingTime((prevTime) => prevTime + 1);\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error(\"Error accessing microphone:\", error);\r\n    }\r\n  };\r\n\r\n  // Stop recording\r\n  const stopRecording = () => {\r\n    if (mediaRecorderRef.current && isRecording) {\r\n      mediaRecorderRef.current.stop();\r\n      setIsRecording(false);\r\n\r\n      // Stop all tracks in the stream\r\n      if (mediaRecorderRef.current.stream) {\r\n        mediaRecorderRef.current.stream\r\n          .getTracks()\r\n          .forEach((track) => track.stop());\r\n      }\r\n\r\n      // Clear timer\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n        timerRef.current = null;\r\n      }\r\n    }\r\n  };\r\n\r\n  // Play recorded audio\r\n  const playAudio = () => {\r\n    if (audioRef.current && audioBlob) {\r\n      // Reset playback to beginning if already at the end\r\n      if (playbackTime >= audioDuration - 0.1) {\r\n        audioRef.current.currentTime = 0;\r\n        setPlaybackTime(0);\r\n      }\r\n\r\n      // Start playback\r\n      audioRef.current\r\n        .play()\r\n        .then(() => {\r\n          setIsPlaying(true);\r\n        })\r\n        .catch((error) => {\r\n          console.error(\"Error playing audio:\", error);\r\n        });\r\n    }\r\n  };\r\n\r\n  // Pause playback\r\n  const pauseAudio = () => {\r\n    if (audioRef.current) {\r\n      audioRef.current.pause();\r\n      setIsPlaying(false);\r\n\r\n      // Store current position\r\n      setPlaybackTime(audioRef.current.currentTime);\r\n    }\r\n  };\r\n\r\n  // Cancel recording\r\n  const handleCancel = () => {\r\n    if (isRecording) {\r\n      stopRecording();\r\n    }\r\n\r\n    if (audioRef.current) {\r\n      audioRef.current.pause();\r\n      audioRef.current = null;\r\n    }\r\n\r\n    setAudioBlob(null);\r\n    setIsPlaying(false);\r\n    onCancel();\r\n  };\r\n\r\n  // Send recorded audio\r\n  const handleSend = () => {\r\n    if (audioBlob) {\r\n      // Create a new blob with MP3 type for better compatibility when sending\r\n      // But keep the original content unchanged\r\n      const blobToSend = new Blob([audioBlob], {\r\n        type: \"audio/mpeg\",\r\n      });\r\n\r\n      // Create a custom blob with duration information\r\n      // We need to use a wrapper object since Blob doesn't support custom properties\r\n      const blobWithDuration = {\r\n        blob: blobToSend,\r\n        recordingDuration: recordingTime,\r\n      };\r\n\r\n      // We'll extract this in the receiver component\r\n\r\n      console.log(\r\n        \"Sending audio blob with duration:\",\r\n        blobWithDuration,\r\n        \"duration:\",\r\n        recordingTime,\r\n      );\r\n      onSend(blobWithDuration.blob, recordingTime);\r\n\r\n      // Clean up\r\n      setAudioBlob(null);\r\n      setIsPlaying(false);\r\n\r\n      if (audioRef.current) {\r\n        audioRef.current.pause();\r\n        audioRef.current = null;\r\n      }\r\n    }\r\n  };\r\n\r\n  // Format time (seconds -> MM:SS)\r\n  const formatTime = (seconds: number) => {\r\n    if (!isFinite(seconds) || seconds < 0) return \"0:00\";\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = Math.floor(seconds % 60);\r\n    return `${mins}:${secs < 10 ? \"0\" : \"\"}${secs}`;\r\n  };\r\n\r\n  // Create and set up audio element when audioBlob changes\r\n  useEffect(() => {\r\n    if (!audioBlob) return;\r\n\r\n    console.log(\"Setting up audio with blob:\", audioBlob);\r\n\r\n    // Create a new audio element with the blob\r\n    const objectUrl = URL.createObjectURL(audioBlob);\r\n    const audio = new Audio();\r\n\r\n    // Set up event listeners before setting src\r\n    const handleLoadedMetadata = () => {\r\n      console.log(\"Audio loaded, duration:\", audio.duration);\r\n      if (isFinite(audio.duration) && audio.duration > 0) {\r\n        setAudioDuration(audio.duration);\r\n      } else {\r\n        console.warn(\"Invalid audio duration:\", audio.duration);\r\n        // Set a default duration if the actual one is invalid\r\n        setAudioDuration(recordingTime || 1);\r\n      }\r\n    };\r\n\r\n    const handleTimeUpdate = () => {\r\n      setPlaybackTime(audio.currentTime);\r\n    };\r\n\r\n    const handleEnded = () => {\r\n      setIsPlaying(false);\r\n    };\r\n\r\n    const handleError = (e: ErrorEvent) => {\r\n      console.error(\"Audio error:\", e);\r\n      // If there's an error, try to use the recording time as a fallback\r\n      setAudioDuration(recordingTime || 1);\r\n    };\r\n\r\n    // Add event listeners\r\n    audio.addEventListener(\"loadedmetadata\", handleLoadedMetadata);\r\n    audio.addEventListener(\"timeupdate\", handleTimeUpdate);\r\n    audio.addEventListener(\"ended\", handleEnded);\r\n    audio.addEventListener(\"error\", handleError);\r\n\r\n    // Now set the source and load\r\n    audio.src = objectUrl;\r\n    audio.load();\r\n\r\n    // Set the audio ref\r\n    audioRef.current = audio;\r\n\r\n    // Clean up function\r\n    return () => {\r\n      // Remove event listeners\r\n      audio.removeEventListener(\"loadedmetadata\", handleLoadedMetadata);\r\n      audio.removeEventListener(\"timeupdate\", handleTimeUpdate);\r\n      audio.removeEventListener(\"ended\", handleEnded);\r\n      audio.removeEventListener(\"error\", handleError);\r\n\r\n      // Clean up resources\r\n      audio.pause();\r\n      audio.src = \"\";\r\n      URL.revokeObjectURL(objectUrl);\r\n    };\r\n  }, [audioBlob, recordingTime]);\r\n\r\n  // Clean up on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (timerRef.current) {\r\n        clearInterval(timerRef.current);\r\n      }\r\n\r\n      if (\r\n        mediaRecorderRef.current &&\r\n        mediaRecorderRef.current.state === \"recording\"\r\n      ) {\r\n        mediaRecorderRef.current.stop();\r\n\r\n        if (mediaRecorderRef.current.stream) {\r\n          mediaRecorderRef.current.stream\r\n            .getTracks()\r\n            .forEach((track) => track.stop());\r\n        }\r\n      }\r\n\r\n      if (audioRef.current) {\r\n        audioRef.current.pause();\r\n        audioRef.current = null;\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Render progress bar for playback with click to seek functionality\r\n  const renderProgressBar = () => {\r\n    // Calculate progress percentage safely\r\n    const progress =\r\n      audioDuration > 0\r\n        ? Math.min(100, Math.max(0, (playbackTime / audioDuration) * 100))\r\n        : 0;\r\n\r\n    // Debug info - uncomment if needed\r\n    // console.log(`Progress: ${progress}%, Time: ${playbackTime}s, Duration: ${audioDuration}s`);\r\n\r\n    const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {\r\n      if (!audioRef.current || audioDuration === 0) return;\r\n\r\n      // Calculate click position relative to the progress bar width\r\n      const progressBar = e.currentTarget;\r\n      const rect = progressBar.getBoundingClientRect();\r\n      const clickPosition = (e.clientX - rect.left) / rect.width;\r\n\r\n      // Set the audio current time based on click position\r\n      const newTime = clickPosition * audioDuration;\r\n      audioRef.current.currentTime = newTime;\r\n      setPlaybackTime(newTime);\r\n    };\r\n\r\n    return (\r\n      <div className=\"w-full\">\r\n        {/* Clickable progress bar */}\r\n        <div\r\n          className=\"w-full h-4 bg-gray-200 rounded-full overflow-hidden cursor-pointer\"\r\n          onClick={handleProgressBarClick}\r\n        >\r\n          <div\r\n            className=\"h-full bg-blue-500 transition-all duration-100\"\r\n            style={{ width: `${progress}%` }}\r\n          ></div>\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full p-2 bg-gray-50 rounded-lg\">\r\n      {!audioBlob ? (\r\n        // Recording state\r\n        <div className=\"flex items-center gap-3\">\r\n          <Button\r\n            variant={isRecording ? \"destructive\" : \"default\"}\r\n            size=\"icon\"\r\n            className={cn(\"h-10 w-10 rounded-full\", {\r\n              \"bg-blue-500 text-white hover:bg-blue-700\": !isRecording,\r\n            })}\r\n            onClick={isRecording ? stopRecording : startRecording}\r\n          >\r\n            {isRecording ? (\r\n              <Square className=\"h-5 w-5\" />\r\n            ) : (\r\n              <Mic className=\"h-5 w-5\" />\r\n            )}\r\n          </Button>\r\n\r\n          <div className=\"flex-1\">\r\n            {isRecording ? (\r\n              <div className=\"flex items-center gap-2\">\r\n                <div className=\"h-2 w-2 rounded-full bg-red-500 animate-pulse\"></div>\r\n                <span className=\"text-sm font-medium\">Đang ghi âm...</span>\r\n                <span className=\"text-xs bg-red-100 text-red-700 px-2 py-0.5 rounded-full\">\r\n                  {formatTime(recordingTime)}\r\n                </span>\r\n              </div>\r\n            ) : (\r\n              <span className=\"text-sm text-gray-500\">\r\n                Nhấn để bắt đầu ghi âm\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          {isRecording && (\r\n            <Button variant=\"ghost\" size=\"sm\" onClick={handleCancel}>\r\n              Hủy\r\n            </Button>\r\n          )}\r\n        </div>\r\n      ) : (\r\n        // Playback state\r\n        <div className=\"flex flex-col gap-2\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-10 w-10 rounded-full bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 flex-shrink-0\"\r\n              onClick={isPlaying ? pauseAudio : playAudio}\r\n            >\r\n              {isPlaying ? (\r\n                <Pause className=\"h-5 w-5\" />\r\n              ) : (\r\n                <Play className=\"h-5 w-5\" />\r\n              )}\r\n            </Button>\r\n\r\n            <div className=\"flex-1 min-w-0\">\r\n              <div className=\"text-sm font-medium mb-2 flex justify-between items-center\">\r\n                <span className=\"flex items-center\">\r\n                  <span className=\"mr-2\">Đoạn ghi âm</span>\r\n                  {isPlaying && (\r\n                    <span className=\"text-xs text-blue-600 animate-pulse\">\r\n                      Đang phát\r\n                    </span>\r\n                  )}\r\n                </span>\r\n                <span className=\"text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full\">\r\n                  {formatTime(audioDuration)}\r\n                </span>\r\n              </div>\r\n              {renderProgressBar()}\r\n            </div>\r\n            <div className=\"flex justify-end gap-2 mt-1\">\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"\"\r\n                onClick={handleCancel}\r\n              >\r\n                <Trash2 className=\"h-4 w-4 text-destructive\" />\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"default\"\r\n                size=\"icon\"\r\n                className=\"rounded-full bg-blue-500 hover:bg-blue-600\"\r\n                onClick={handleSend}\r\n              >\r\n                <Send className=\"h-4 w-4 \" />\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,cAAc,EACpC,MAAM,EACN,QAAQ,EACW;;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACtD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU,EAAE;IACxC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAC/C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IAEjD,kBAAkB;IAClB,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,SAAS,MAAM,UAAU,YAAY,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAK;YACvE,MAAM,gBAAgB,IAAI,cAAc;YACxC,iBAAiB,OAAO,GAAG;YAC3B,eAAe,OAAO,GAAG,EAAE;YAE3B,cAAc,eAAe,GAAG,CAAC;gBAC/B,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG,GAAG;oBACvB,eAAe,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI;gBACxC;YACF;YAEA,cAAc,MAAM,GAAG;gBACrB,kDAAkD;gBAClD,0FAA0F;gBAC1F,kDAAkD;gBAClD,IAAI;gBAEJ,kEAAkE;gBAClE,IAAI;oBACF,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;wBAAE,MAAM;oBAAa;oBAClE,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,wBAAwB;oBACxB,IAAI;wBACF,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;4BAAE,MAAM;wBAAY;wBACjE,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,QAAQ;wBACf,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,sCAAsC;wBACtC,YAAY,IAAI,KAAK,eAAe,OAAO,EAAE;4BAC3C,MAAM;wBACR;wBACA,QAAQ,GAAG,CAAC;oBACd;gBACF;gBAEA,wEAAwE;gBACxE,aAAa;gBACb,iBAAiB,iBAAiB,IAAI,4CAA4C;YACpF;YAEA,cAAc,KAAK;YACnB,eAAe;YACf,iBAAiB;YAEjB,cAAc;YACd,SAAS,OAAO,GAAG,YAAY;gBAC7B,iBAAiB,CAAC,WAAa,WAAW;YAC5C,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB;QACpB,IAAI,iBAAiB,OAAO,IAAI,aAAa;YAC3C,iBAAiB,OAAO,CAAC,IAAI;YAC7B,eAAe;YAEf,gCAAgC;YAChC,IAAI,iBAAiB,OAAO,CAAC,MAAM,EAAE;gBACnC,iBAAiB,OAAO,CAAC,MAAM,CAC5B,SAAS,GACT,OAAO,CAAC,CAAC,QAAU,MAAM,IAAI;YAClC;YAEA,cAAc;YACd,IAAI,SAAS,OAAO,EAAE;gBACpB,cAAc,SAAS,OAAO;gBAC9B,SAAS,OAAO,GAAG;YACrB;QACF;IACF;IAEA,sBAAsB;IACtB,MAAM,YAAY;QAChB,IAAI,SAAS,OAAO,IAAI,WAAW;YACjC,oDAAoD;YACpD,IAAI,gBAAgB,gBAAgB,KAAK;gBACvC,SAAS,OAAO,CAAC,WAAW,GAAG;gBAC/B,gBAAgB;YAClB;YAEA,iBAAiB;YACjB,SAAS,OAAO,CACb,IAAI,GACJ,IAAI,CAAC;gBACJ,aAAa;YACf,GACC,KAAK,CAAC,CAAC;gBACN,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACJ;IACF;IAEA,iBAAiB;IACjB,MAAM,aAAa;QACjB,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;YACtB,aAAa;YAEb,yBAAyB;YACzB,gBAAgB,SAAS,OAAO,CAAC,WAAW;QAC9C;IACF;IAEA,mBAAmB;IACnB,MAAM,eAAe;QACnB,IAAI,aAAa;YACf;QACF;QAEA,IAAI,SAAS,OAAO,EAAE;YACpB,SAAS,OAAO,CAAC,KAAK;YACtB,SAAS,OAAO,GAAG;QACrB;QAEA,aAAa;QACb,aAAa;QACb;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa;QACjB,IAAI,WAAW;YACb,wEAAwE;YACxE,0CAA0C;YAC1C,MAAM,aAAa,IAAI,KAAK;gBAAC;aAAU,EAAE;gBACvC,MAAM;YACR;YAEA,iDAAiD;YACjD,+EAA+E;YAC/E,MAAM,mBAAmB;gBACvB,MAAM;gBACN,mBAAmB;YACrB;YAEA,+CAA+C;YAE/C,QAAQ,GAAG,CACT,qCACA,kBACA,aACA;YAEF,OAAO,iBAAiB,IAAI,EAAE;YAE9B,WAAW;YACX,aAAa;YACb,aAAa;YAEb,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,KAAK;gBACtB,SAAS,OAAO,GAAG;YACrB;QACF;IACF;IAEA,iCAAiC;IACjC,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,SAAS,YAAY,UAAU,GAAG,OAAO;QAC9C,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,KAAK,MAAM;IACjD;IAEA,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,2CAA2C;YAC3C,MAAM,YAAY,IAAI,eAAe,CAAC;YACtC,MAAM,QAAQ,IAAI;YAElB,4CAA4C;YAC5C,MAAM;gEAAuB;oBAC3B,QAAQ,GAAG,CAAC,2BAA2B,MAAM,QAAQ;oBACrD,IAAI,SAAS,MAAM,QAAQ,KAAK,MAAM,QAAQ,GAAG,GAAG;wBAClD,iBAAiB,MAAM,QAAQ;oBACjC,OAAO;wBACL,QAAQ,IAAI,CAAC,2BAA2B,MAAM,QAAQ;wBACtD,sDAAsD;wBACtD,iBAAiB,iBAAiB;oBACpC;gBACF;;YAEA,MAAM;4DAAmB;oBACvB,gBAAgB,MAAM,WAAW;gBACnC;;YAEA,MAAM;uDAAc;oBAClB,aAAa;gBACf;;YAEA,MAAM;uDAAc,CAAC;oBACnB,QAAQ,KAAK,CAAC,gBAAgB;oBAC9B,mEAAmE;oBACnE,iBAAiB,iBAAiB;gBACpC;;YAEA,sBAAsB;YACtB,MAAM,gBAAgB,CAAC,kBAAkB;YACzC,MAAM,gBAAgB,CAAC,cAAc;YACrC,MAAM,gBAAgB,CAAC,SAAS;YAChC,MAAM,gBAAgB,CAAC,SAAS;YAEhC,8BAA8B;YAC9B,MAAM,GAAG,GAAG;YACZ,MAAM,IAAI;YAEV,oBAAoB;YACpB,SAAS,OAAO,GAAG;YAEnB,oBAAoB;YACpB;2CAAO;oBACL,yBAAyB;oBACzB,MAAM,mBAAmB,CAAC,kBAAkB;oBAC5C,MAAM,mBAAmB,CAAC,cAAc;oBACxC,MAAM,mBAAmB,CAAC,SAAS;oBACnC,MAAM,mBAAmB,CAAC,SAAS;oBAEnC,qBAAqB;oBACrB,MAAM,KAAK;oBACX,MAAM,GAAG,GAAG;oBACZ,IAAI,eAAe,CAAC;gBACtB;;QACF;kCAAG;QAAC;QAAW;KAAc;IAE7B,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;2CAAO;oBACL,IAAI,SAAS,OAAO,EAAE;wBACpB,cAAc,SAAS,OAAO;oBAChC;oBAEA,IACE,iBAAiB,OAAO,IACxB,iBAAiB,OAAO,CAAC,KAAK,KAAK,aACnC;wBACA,iBAAiB,OAAO,CAAC,IAAI;wBAE7B,IAAI,iBAAiB,OAAO,CAAC,MAAM,EAAE;4BACnC,iBAAiB,OAAO,CAAC,MAAM,CAC5B,SAAS,GACT,OAAO;2DAAC,CAAC,QAAU,MAAM,IAAI;;wBAClC;oBACF;oBAEA,IAAI,SAAS,OAAO,EAAE;wBACpB,SAAS,OAAO,CAAC,KAAK;wBACtB,SAAS,OAAO,GAAG;oBACrB;gBACF;;QACF;kCAAG,EAAE;IAEL,oEAAoE;IACpE,MAAM,oBAAoB;QACxB,uCAAuC;QACvC,MAAM,WACJ,gBAAgB,IACZ,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,AAAC,eAAe,gBAAiB,QAC3D;QAEN,mCAAmC;QACnC,8FAA8F;QAE9F,MAAM,yBAAyB,CAAC;YAC9B,IAAI,CAAC,SAAS,OAAO,IAAI,kBAAkB,GAAG;YAE9C,8DAA8D;YAC9D,MAAM,cAAc,EAAE,aAAa;YACnC,MAAM,OAAO,YAAY,qBAAqB;YAC9C,MAAM,gBAAgB,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;YAE1D,qDAAqD;YACrD,MAAM,UAAU,gBAAgB;YAChC,SAAS,OAAO,CAAC,WAAW,GAAG;YAC/B,gBAAgB;QAClB;QAEA,qBACE,6LAAC;YAAI,WAAU;sBAEb,cAAA,6LAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;IAKzC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,CAAC,YACA,kBAAkB;sBAClB,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,cAAc,gBAAgB;oBACvC,MAAK;oBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;wBACtC,4CAA4C,CAAC;oBAC/C;oBACA,SAAS,cAAc,gBAAgB;8BAEtC,4BACC,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;6CAElB,6LAAC,mMAAA,CAAA,MAAG;wBAAC,WAAU;;;;;;;;;;;8BAInB,6LAAC;oBAAI,WAAU;8BACZ,4BACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;0CACtC,6LAAC;gCAAK,WAAU;0CACb,WAAW;;;;;;;;;;;6CAIhB,6LAAC;wBAAK,WAAU;kCAAwB;;;;;;;;;;;gBAM3C,6BACC,6LAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;oBAAK,SAAS;8BAAc;;;;;;;;;;;mBAM7D,iBAAiB;sBACjB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,YAAY,aAAa;kCAEjC,0BACC,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAIpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;0DACd,6LAAC;gDAAK,WAAU;0DAAO;;;;;;4CACtB,2BACC,6LAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;;kDAK1D,6LAAC;wCAAK,WAAU;kDACb,WAAW;;;;;;;;;;;;4BAGf;;;;;;;kCAEH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;GAnawB;KAAA", "debugId": null}}, {"offset": {"line": 3873, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/MessageInput.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { useState, useRef, useEffect, DragEvent, useCallback } from \"react\";\r\nimport {\r\n  Paperclip,\r\n  Smile,\r\n  Send,\r\n  X,\r\n  Reply,\r\n  ImageIcon,\r\n  FileText,\r\n  Play,\r\n  Trash2,\r\n  AlertCircle,\r\n  Music,\r\n  Mic,\r\n  Loader2,\r\n  Sparkles,\r\n  Mail,\r\n} from \"lucide-react\";\r\nimport AudioRecorder from \"./AudioRecorder\";\r\nimport { toast } from \"sonner\";\r\nimport { Message } from \"@/types/base\";\r\nimport EmojiPicker, { EmojiClickData } from \"emoji-picker-react\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport Image from \"next/image\";\r\nimport { enhanceMessage } from \"@/actions/ai.action\";\r\nimport AIEmailDialog from \"../email-generate/AIEmailDialog\";\r\n\r\ninterface MessageInputProps {\r\n  onSendMessage: (message: string, files?: File[]) => void;\r\n  disabled?: boolean;\r\n  replyingTo?: Message | null;\r\n  onCancelReply?: () => void;\r\n}\r\n\r\nexport default function MessageInput({\r\n  onSendMessage,\r\n  disabled = false,\r\n  replyingTo,\r\n  onCancelReply,\r\n}: MessageInputProps) {\r\n  const [message, setMessage] = useState(\"\");\r\n  const [showEmojiPicker, setShowEmojiPicker] = useState(false);\r\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\r\n  const [previewUrls, setPreviewUrls] = useState<\r\n    { file: File; url: string; type: string }[]\r\n  >([]);\r\n  const [isDragging, setIsDragging] = useState(false);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const [isEnhancing, setIsEnhancing] = useState(false);\r\n  const [isEnhancedMessage, setIsEnhancedMessage] = useState(false);\r\n  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const emojiPickerRef = useRef<HTMLDivElement>(null);\r\n  const emojiButtonRef = useRef<HTMLButtonElement>(null);\r\n  const imageInputRef = useRef<HTMLInputElement>(null);\r\n  const audioInputRef = useRef<HTMLInputElement>(null);\r\n  const documentInputRef = useRef<HTMLInputElement>(null);\r\n  const inputContainerRef = useRef<HTMLDivElement>(null);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const sendTypingIndicator = useChatStore(\r\n    (state) => state.sendTypingIndicator,\r\n  );\r\n  const messages = useChatStore((state) => state.messages);\r\n  const selectedContact = useChatStore((state) => state.selectedContact);\r\n\r\n  // Danh sách các định dạng file an toàn được chấp nhận\r\n  const safeDocumentTypes = [\r\n    \".pdf\",\r\n    \".doc\",\r\n    \".docx\",\r\n    \".xls\",\r\n    \".xlsx\",\r\n    \".ppt\",\r\n    \".pptx\",\r\n    \".txt\",\r\n    \".csv\",\r\n    \".rtf\",\r\n    \".odt\",\r\n    \".ods\",\r\n    \".odp\",\r\n  ];\r\n\r\n  // Tự động điều chỉnh chiều cao của textarea\r\n  const adjustTextareaHeight = () => {\r\n    const textarea = textareaRef.current;\r\n    if (textarea) {\r\n      textarea.style.height = \"auto\";\r\n      const newHeight = Math.min(textarea.scrollHeight, 100); // Giới hạn chiều cao tối đa là 100px (khoảng 4 dòng)\r\n      textarea.style.height = `${newHeight}px`;\r\n    }\r\n  };\r\n\r\n  // Xử lý khi chọn file\r\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    if (e.target.files) {\r\n      const newFiles = Array.from(e.target.files);\r\n      // Kiểm tra xem có phải là file an toàn không\r\n      const safeFiles = newFiles.filter((file) => isSafeFile(file));\r\n\r\n      if (safeFiles.length !== newFiles.length) {\r\n        const rejectedCount = newFiles.length - safeFiles.length;\r\n        toast.error(\r\n          `${rejectedCount} file không được chấp nhận do không an toàn`,\r\n          {\r\n            description:\r\n              \"Chỉ chấp nhận hình ảnh, video, âm thanh và các tài liệu văn phòng phổ biến\",\r\n            icon: <AlertCircle className=\"h-5 w-5\" />,\r\n          },\r\n        );\r\n      }\r\n\r\n      addFiles(safeFiles);\r\n    }\r\n  };\r\n\r\n  // Kiểm tra xem file có an toàn không\r\n  const isSafeFile = (file: File): boolean => {\r\n    const fileType = file.type.split(\"/\")[0]; // image, video, audio, application, etc.\r\n    const fileExtension = `.${file.name.split(\".\").pop()?.toLowerCase() || \"\"}`;\r\n\r\n    // Chấp nhận hình ảnh, video và âm thanh\r\n    if (fileType === \"image\" || fileType === \"video\" || fileType === \"audio\") {\r\n      return true;\r\n    }\r\n\r\n    // Kiểm tra các định dạng tài liệu an toàn\r\n    if (safeDocumentTypes.includes(fileExtension)) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  // Hàm chung để xử lý thêm files - sử dụng useCallback để tránh re-render không cần thiết\r\n  const addFiles = useCallback((newFiles: File[]) => {\r\n    setSelectedFiles((prev) => [...prev, ...newFiles]);\r\n\r\n    // Tạo preview URLs cho các file\r\n    newFiles.forEach((file) => {\r\n      const fileType = file.type.split(\"/\")[0]; // image, video, application, etc.\r\n\r\n      if (fileType === \"image\") {\r\n        const url = URL.createObjectURL(file);\r\n        setPreviewUrls((prev) => [...prev, { file, url, type: \"image\" }]);\r\n      } else if (fileType === \"video\") {\r\n        const url = URL.createObjectURL(file);\r\n        setPreviewUrls((prev) => [...prev, { file, url, type: \"video\" }]);\r\n      } else if (fileType === \"audio\") {\r\n        const url = URL.createObjectURL(file);\r\n        setPreviewUrls((prev) => [...prev, { file, url, type: \"audio\" }]);\r\n      } else {\r\n        // For other file types (documents, etc.)\r\n        setPreviewUrls((prev) => [...prev, { file, url: \"\", type: \"file\" }]);\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  // Xử lý khi kéo file vào\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n  };\r\n\r\n  const handleDrop = (\r\n    e: DragEvent<HTMLDivElement>,\r\n    sendImmediately = false,\r\n  ) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setIsDragging(false);\r\n\r\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n      const newFiles = Array.from(e.dataTransfer.files);\r\n      // Lọc các file an toàn\r\n      const safeFiles = newFiles.filter((file) => isSafeFile(file));\r\n\r\n      if (safeFiles.length !== newFiles.length) {\r\n        const rejectedCount = newFiles.length - safeFiles.length;\r\n        toast.error(\r\n          `${rejectedCount} file không được chấp nhận do không an toàn`,\r\n          {\r\n            description:\r\n              \"Chỉ chấp nhận hình ảnh, video, âm thanh và các tài liệu văn phòng phổ biến\",\r\n            icon: <AlertCircle className=\"h-5 w-5\" />,\r\n          },\r\n        );\r\n      }\r\n\r\n      if (sendImmediately) {\r\n        // Gửi ngay lập tức nếu thả vào khu vực xem tin nhắn\r\n        onSendMessage(\"\", safeFiles);\r\n      } else {\r\n        // Hiển thị preview nếu thả vào ô input\r\n        addFiles(safeFiles);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Xử lý khi xóa file\r\n  const handleRemoveFile = (fileToRemove: File) => {\r\n    setSelectedFiles((prev) => prev.filter((file) => file !== fileToRemove));\r\n    setPreviewUrls((prev) => {\r\n      const filtered = prev.filter((item) => item.file !== fileToRemove);\r\n      // Revoke object URL to avoid memory leaks\r\n      const itemToRemove = prev.find((item) => item.file === fileToRemove);\r\n      if (itemToRemove && itemToRemove.url) {\r\n        URL.revokeObjectURL(itemToRemove.url);\r\n      }\r\n      return filtered;\r\n    });\r\n  };\r\n\r\n  // Xử lý khi xóa tất cả file\r\n  const handleRemoveAllFiles = () => {\r\n    // Revoke all object URLs\r\n    previewUrls.forEach((item) => {\r\n      if (item.url) URL.revokeObjectURL(item.url);\r\n    });\r\n    setSelectedFiles([]);\r\n    setPreviewUrls([]);\r\n  };\r\n\r\n  // Xử lý khi nhấn nút đính kèm hình ảnh/video\r\n  const handleImageAttachClick = () => {\r\n    imageInputRef.current?.click();\r\n  };\r\n\r\n  // Xử lý khi nhấn nút đính kèm âm thanh\r\n  const handleAudioAttachClick = () => {\r\n    audioInputRef.current?.click();\r\n  };\r\n\r\n  // Xử lý khi nhấn nút đính kèm tài liệu\r\n  const handleDocumentAttachClick = () => {\r\n    documentInputRef.current?.click();\r\n  };\r\n\r\n  const handleSendMessage = () => {\r\n    if ((message.trim() || selectedFiles.length > 0) && !disabled) {\r\n      // Stop typing indicator when sending message\r\n      if (isTyping && sendTypingIndicator) {\r\n        setIsTyping(false);\r\n        sendTypingIndicator(false);\r\n\r\n        if (typingTimeoutRef.current) {\r\n          clearTimeout(typingTimeoutRef.current);\r\n          typingTimeoutRef.current = null;\r\n        }\r\n      }\r\n\r\n      onSendMessage(\r\n        message,\r\n        selectedFiles.length > 0 ? selectedFiles : undefined,\r\n      );\r\n      setMessage(\"\");\r\n      setIsEnhancedMessage(false);\r\n      handleRemoveAllFiles(); // Clear files after sending\r\n    }\r\n  };\r\n\r\n  // Handle textarea change\r\n  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    setMessage(e.target.value);\r\n    // If user starts typing after AI enhancement, clear the enhanced status\r\n    if (isEnhancedMessage) {\r\n      setIsEnhancedMessage(false);\r\n    }\r\n    // Điều chỉnh chiều cao sau khi nội dung thay đổi\r\n    setTimeout(adjustTextareaHeight, 0);\r\n    // Send typing indicator\r\n    handleTyping();\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    if (e.key === \"Enter\" && !e.shiftKey) {\r\n      // Prevent immediate sending of enhanced messages if they haven't been modified\r\n      if (isEnhancing) {\r\n        e.preventDefault();\r\n        return;\r\n      }\r\n\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  // Handle typing indicator\r\n  const handleTyping = useCallback(() => {\r\n    if (!isTyping && sendTypingIndicator) {\r\n      setIsTyping(true);\r\n      sendTypingIndicator(true);\r\n    }\r\n\r\n    // Clear existing timeout\r\n    if (typingTimeoutRef.current) {\r\n      clearTimeout(typingTimeoutRef.current);\r\n    }\r\n\r\n    // Set new timeout to stop typing indicator after 2 seconds of inactivity\r\n    typingTimeoutRef.current = setTimeout(() => {\r\n      if (isTyping && sendTypingIndicator) {\r\n        setIsTyping(false);\r\n        sendTypingIndicator(false);\r\n      }\r\n    }, 2000);\r\n  }, [isTyping, sendTypingIndicator]);\r\n\r\n  const handleEmojiClick = (emojiData: EmojiClickData) => {\r\n    setMessage((prev) => prev + emojiData.emoji);\r\n  };\r\n\r\n  // Đóng emoji picker khi click ra ngoài\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (\r\n        emojiPickerRef.current &&\r\n        !emojiPickerRef.current.contains(event.target as Node) &&\r\n        emojiButtonRef.current &&\r\n        !emojiButtonRef.current.contains(event.target as Node)\r\n      ) {\r\n        setShowEmojiPicker(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  // Điều chỉnh chiều cao của textarea khi component mount và khi message thay đổi\r\n  useEffect(() => {\r\n    adjustTextareaHeight();\r\n  }, [message]);\r\n\r\n  // Xử lý sự kiện paste để hỗ trợ dán ảnh trực tiếp\r\n  const handlePaste = useCallback(\r\n    (e: ClipboardEvent) => {\r\n      if (disabled) return;\r\n\r\n      const items = e.clipboardData?.items;\r\n      if (!items) return;\r\n\r\n      let hasImageItems = false;\r\n      const imageFiles: File[] = [];\r\n\r\n      // Kiểm tra xem có ảnh trong clipboard không\r\n      for (let i = 0; i < items.length; i++) {\r\n        const item = items[i];\r\n\r\n        // Chỉ xử lý các item loại image\r\n        if (item.type.startsWith(\"image/\")) {\r\n          hasImageItems = true;\r\n          const file = item.getAsFile();\r\n          if (file) {\r\n            // Đổi tên file để dễ nhận biết\r\n            const timestamp = Date.now();\r\n            const newFile = new File([file], `screenshot_${timestamp}.png`, {\r\n              type: file.type,\r\n              lastModified: timestamp,\r\n            });\r\n            imageFiles.push(newFile);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Nếu có ảnh, thêm vào danh sách file đính kèm\r\n      if (hasImageItems && imageFiles.length > 0) {\r\n        e.preventDefault(); // Ngăn chặn hành vi paste mặc định\r\n        addFiles(imageFiles);\r\n        toast.success(`Đã thêm ${imageFiles.length} ảnh từ clipboard`);\r\n      }\r\n    },\r\n    [disabled, addFiles],\r\n  );\r\n\r\n  // Cleanup typing timeout when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      if (typingTimeoutRef.current) {\r\n        clearTimeout(typingTimeoutRef.current);\r\n        // Make sure to send typing stopped when unmounting\r\n        if (isTyping && sendTypingIndicator) {\r\n          sendTypingIndicator(false);\r\n        }\r\n      }\r\n    };\r\n  }, [isTyping, sendTypingIndicator]);\r\n\r\n  // Thêm event listener cho sự kiện paste\r\n  useEffect(() => {\r\n    // Thêm event listener cho textarea\r\n    const textareaElement = textareaRef.current;\r\n    if (textareaElement) {\r\n      textareaElement.addEventListener(\"paste\", handlePaste);\r\n    }\r\n\r\n    // Cleanup khi component unmount\r\n    return () => {\r\n      if (textareaElement) {\r\n        textareaElement.removeEventListener(\"paste\", handlePaste);\r\n      }\r\n    };\r\n  }, [handlePaste]);\r\n\r\n  // Helper function to get a preview of the message content\r\n  const getMessagePreview = (message: Message): string => {\r\n    if (message.content.text) {\r\n      return message.content.text;\r\n    }\r\n\r\n    if (message.content.media && message.content.media.length > 0) {\r\n      const mediaCount = message.content.media.length;\r\n      const firstMedia = message.content.media[0];\r\n\r\n      if (mediaCount === 1) {\r\n        if (firstMedia.type === \"IMAGE\") {\r\n          return `[Hình ảnh: ${firstMedia.fileName}]`;\r\n        } else if (firstMedia.type === \"VIDEO\") {\r\n          return `[Video: ${firstMedia.fileName}]`;\r\n        } else {\r\n          return `[Tệp: ${firstMedia.fileName}]`;\r\n        }\r\n      } else {\r\n        // Multiple media items\r\n        if (firstMedia.type === \"IMAGE\") {\r\n          return `[${mediaCount} hình ảnh]`;\r\n        } else if (firstMedia.type === \"VIDEO\") {\r\n          return `[${mediaCount} video]`;\r\n        } else {\r\n          return `[${mediaCount} tệp]`;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (message.content.image) {\r\n      return \"[Hình ảnh]\";\r\n    }\r\n\r\n    if (message.content.video) {\r\n      return \"[Video]\";\r\n    }\r\n\r\n    return \"\";\r\n  };\r\n\r\n  // Xử lý tăng cường tin nhắn bằng AI\r\n  const handleEnhanceMessage = async () => {\r\n    // Prevent enhancing if already processing or empty message\r\n    if (!message.trim() || disabled || isEnhancing) return;\r\n\r\n    // Check for minimum character requirement (10 characters)\r\n    if (message.trim().length < 10) {\r\n      toast.error(\"Không thể tăng cường\", {\r\n        description: \"Tin nhắn cần có ít nhất 10 ký tự để tăng cường bằng AI\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsEnhancing(true);\r\n    try {\r\n      // Store original message in case enhancement fails\r\n      const originalMessage = message;\r\n\r\n      // Lấy 5 tin nhắn gần nhất để cung cấp ngữ cảnh cho AI\r\n      const recentMessages = messages\r\n        .slice(-5)\r\n        .filter((m) => !m.recalled) // Lọc bỏ các tin nhắn đã thu hồi\r\n        .map((m) => ({\r\n          content: m.content.text || \"\",\r\n          type: m.sender?.id === selectedContact?.id ? \"contact\" : \"user\",\r\n          senderId: m.sender?.id || \"\",\r\n          senderName: m.sender?.userInfo?.fullName || \"\",\r\n        }))\r\n        .filter((m) => m.content.trim() !== \"\"); // Lọc bỏ tin nhắn rỗng\r\n\r\n      // Temporary clear message to prevent double sending\r\n      setMessage(\"\");\r\n\r\n      const result = await enhanceMessage(\r\n        originalMessage,\r\n        recentMessages.length > 0 ? recentMessages : undefined,\r\n      );\r\n\r\n      if (result.success && result.enhancedMessage) {\r\n        // Mark message as enhanced to prevent automatic duplication\r\n        setIsEnhancedMessage(true);\r\n        setMessage(result.enhancedMessage);\r\n        toast.success(\"Tin nhắn đã được tăng cường\", {\r\n          description: \"Nội dung đã được cải thiện bởi AI\",\r\n        });\r\n        // Focus input after enhancement\r\n        setTimeout(() => {\r\n          textareaRef.current?.focus();\r\n        }, 0);\r\n      } else {\r\n        // Restore original message if enhancement failed\r\n        setMessage(originalMessage);\r\n        toast.error(\"Không thể tăng cường tin nhắn\", {\r\n          description: result.error || \"Đã xảy ra lỗi khi tăng cường tin nhắn\",\r\n        });\r\n      }\r\n    } catch {\r\n      // Restore message in case of error\r\n      setMessage(message);\r\n      toast.error(\"Không thể tăng cường tin nhắn\", {\r\n        description: \"Đã xảy ra lỗi khi kết nối đến dịch vụ AI\",\r\n      });\r\n    } finally {\r\n      setIsEnhancing(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Reply preview */}\r\n      {replyingTo && (\r\n        <div className=\"px-3 py-2 border-b flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <div className=\"bg-gray-100 p-1 rounded-full\">\r\n              <Reply className=\"h-4 w-4 text-gray-600\" />\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <span className=\"text-sm font-medium\">Trả lời</span>\r\n              <Avatar className=\"h-5 w-5\">\r\n                <AvatarImage\r\n                  src={\r\n                    replyingTo.sender?.userInfo?.profilePictureUrl || undefined\r\n                  }\r\n                />\r\n                <AvatarFallback>\r\n                  {replyingTo.sender?.userInfo?.fullName\r\n                    ?.slice(0, 2)\r\n                    .toUpperCase() || \"??\"}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <span className=\"text-sm text-gray-600 truncate max-w-[200px]\">\r\n                {replyingTo.recalled\r\n                  ? \"Tin nhắn đã được thu hồi\"\r\n                  : getMessagePreview(replyingTo)}\r\n              </span>\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"h-6 w-6 rounded-full hover:bg-gray-100\"\r\n            onClick={onCancelReply}\r\n          >\r\n            <X className=\"h-4 w-4 text-gray-500\" />\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {/* File preview area */}\r\n      {previewUrls.length > 0 && (\r\n        <div className=\"px-3 py-2 border-t flex flex-wrap gap-2 items-center\">\r\n          <div className=\"flex-1 flex flex-wrap gap-2\">\r\n            {previewUrls.map((item, index) => (\r\n              <div key={index} className=\"relative group\">\r\n                {item.type === \"image\" ? (\r\n                  <div className=\"w-16 h-16 rounded overflow-hidden border\">\r\n                    <Image\r\n                      src={item.url}\r\n                      alt={item.file.name}\r\n                      width={500}\r\n                      height={500}\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                  </div>\r\n                ) : item.type === \"video\" ? (\r\n                  <div className=\"w-16 h-16 rounded overflow-hidden border bg-gray-100 flex items-center justify-center\">\r\n                    <video\r\n                      src={item.url}\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                    <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                      <Play className=\"h-6 w-6 text-white\" />\r\n                    </div>\r\n                  </div>\r\n                ) : item.type === \"audio\" ? (\r\n                  <div className=\"w-16 h-16 rounded overflow-hidden border bg-gray-100 flex flex-col items-center justify-center p-1\">\r\n                    <Music className=\"h-6 w-6 text-gray-500\" />\r\n                    <span className=\"text-xs text-gray-500 truncate w-full text-center mt-1\">\r\n                      {item.file.name.split(\".\").pop()?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"w-16 h-16 rounded overflow-hidden border bg-gray-100 flex flex-col items-center justify-center p-1\">\r\n                    <FileText className=\"h-6 w-6 text-gray-500\" />\r\n                    <span className=\"text-xs text-gray-500 truncate w-full text-center mt-1\">\r\n                      {item.file.name.split(\".\").pop()?.toUpperCase()}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                <button\r\n                  className=\"absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                  onClick={() => handleRemoveFile(item.file)}\r\n                >\r\n                  <X className=\"h-3 w-3\" />\r\n                </button>\r\n              </div>\r\n            ))}\r\n          </div>\r\n          {previewUrls.length > 0 && (\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"text-red-500 hover:text-red-700 hover:bg-red-50\"\r\n              onClick={handleRemoveAllFiles}\r\n            >\r\n              <Trash2 />\r\n            </Button>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      <div\r\n        className=\"flex flex-col border-t bg-white\"\r\n        onDragOver={handleDragOver}\r\n        onDragLeave={handleDragLeave}\r\n        onDrop={(e) => handleDrop(e)}\r\n      >\r\n        {/* Hidden file inputs */}\r\n        <input\r\n          type=\"file\"\r\n          ref={imageInputRef}\r\n          className=\"hidden\"\r\n          onChange={handleFileChange}\r\n          multiple\r\n          accept=\"image/*,video/*\"\r\n        />\r\n        <input\r\n          type=\"file\"\r\n          ref={audioInputRef}\r\n          className=\"hidden\"\r\n          onChange={handleFileChange}\r\n          multiple\r\n          accept=\"audio/*\"\r\n        />\r\n        <input\r\n          type=\"file\"\r\n          ref={documentInputRef}\r\n          className=\"hidden\"\r\n          onChange={handleFileChange}\r\n          multiple\r\n          accept=\".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.rtf,.odt,.ods,.odp\"\r\n        />\r\n\r\n        {/* Toolbar buttons - now above the input */}\r\n        <div className=\"flex items-center py-0.5 px-2 border-b\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={disabled}\r\n            onClick={handleImageAttachClick}\r\n            title=\"Đính kèm hình ảnh hoặc video\"\r\n          >\r\n            <ImageIcon className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={disabled}\r\n            onClick={handleAudioAttachClick}\r\n            title=\"Đính kèm âm thanh\"\r\n          >\r\n            <Music className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={disabled}\r\n            onClick={handleDocumentAttachClick}\r\n            title=\"Đính kèm tài liệu\"\r\n          >\r\n            <Paperclip className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={disabled}\r\n            onClick={() => setIsRecording(true)}\r\n            title=\"Ghi âm tin nhắn\"\r\n          >\r\n            <Mic className=\"h-5 w-5\" />\r\n          </Button>\r\n\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            disabled={disabled || !message.trim() || isEnhancing}\r\n            onClick={handleEnhanceMessage}\r\n            title={\r\n              message.trim().length < 10\r\n                ? \"Tin nhắn cần ít nhất 10 ký tự để tăng cường\"\r\n                : \"Tăng cường tin nhắn bằng AI\"\r\n            }\r\n            className={isEnhancing ? \"text-blue-500\" : \"\"}\r\n          >\r\n            {isEnhancing ? (\r\n              <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n            ) : (\r\n              <Sparkles className=\"h-5 w-5\" />\r\n            )}\r\n          </Button>\r\n        </div>\r\n\r\n        {/* Audio recorder */}\r\n        {isRecording && (\r\n          <AudioRecorder\r\n            onSend={(audioBlob, duration) => {\r\n              console.log(\r\n                \"Received audio blob from recorder:\",\r\n                audioBlob,\r\n                \"duration:\",\r\n                duration,\r\n              );\r\n\r\n              // Create filename with duration info if available\r\n              const timestamp = Date.now();\r\n              const fileName = duration\r\n                ? `audio_message_${timestamp}_duration_${duration}.mp3`\r\n                : `audio_message_${timestamp}.mp3`;\r\n\r\n              // Convert blob to File object with correct MIME type\r\n              const audioFile = new File([audioBlob], fileName, {\r\n                type: audioBlob.type || \"audio/mpeg\",\r\n                lastModified: timestamp,\r\n              });\r\n\r\n              // Add custom property for duration if available\r\n              if (duration) {\r\n                Object.defineProperty(audioFile, \"duration\", {\r\n                  value: duration,\r\n                  writable: false,\r\n                });\r\n              }\r\n\r\n              console.log(\r\n                \"Created audio file:\",\r\n                audioFile,\r\n                \"with duration:\",\r\n                duration,\r\n              );\r\n\r\n              // Send the audio file\r\n              onSendMessage(\"\", [audioFile]);\r\n              setIsRecording(false);\r\n            }}\r\n            onCancel={() => setIsRecording(false)}\r\n          />\r\n        )}\r\n\r\n        {/* Input field and send button */}\r\n        {!isRecording && (\r\n          <div\r\n            className={`flex items-center p-2 ${isDragging ? \"bg-blue-50 border border-dashed border-blue-300 rounded-md\" : \"\"}`}\r\n            ref={inputContainerRef}\r\n          >\r\n            <div className=\"flex-1 relative\">\r\n              <textarea\r\n                ref={textareaRef}\r\n                placeholder={\r\n                  disabled ? \"Chọn một cuộc trò chuyện\" : \"Nhập tin nhắn...\"\r\n                }\r\n                className=\"w-full p-2 pl-3 pr-10 rounded-md focus:outline-none focus:ring-none resize-none overflow-auto min-h-[40px] max-h-[100px]\"\r\n                value={message}\r\n                onChange={handleTextareaChange}\r\n                onKeyDown={handleKeyDown}\r\n                disabled={disabled}\r\n                rows={1}\r\n                style={{ height: message ? \"auto\" : \"40px\" }}\r\n              />\r\n\r\n              <div className=\"absolute right-2 top-1/2 -translate-y-1/2\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className={`h-7 w-7 rounded-full ${showEmojiPicker ? \"text-blue-500\" : \"text-gray-500\"} hover:text-blue-500 hover:bg-transparent`}\r\n                  disabled={disabled}\r\n                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}\r\n                  ref={emojiButtonRef}\r\n                >\r\n                  <Smile className=\"h-5 w-5\" />\r\n                </Button>\r\n              </div>\r\n\r\n              {showEmojiPicker && (\r\n                <div\r\n                  className=\"absolute bottom-12 right-0 z-50 shadow-lg rounded-lg overflow-hidden transition-all duration-200 transform origin-bottom-right\"\r\n                  ref={emojiPickerRef}\r\n                >\r\n                  <EmojiPicker\r\n                    onEmojiClick={handleEmojiClick}\r\n                    width={320}\r\n                    height={400}\r\n                    searchPlaceHolder=\"Tìm kiếm emoji...\"\r\n                    previewConfig={{ showPreview: false }}\r\n                    lazyLoadEmojis={true}\r\n                    skinTonesDisabled\r\n                  />\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Send button */}\r\n            <Button\r\n              variant={\r\n                message.trim() || selectedFiles.length > 0 ? \"default\" : \"ghost\"\r\n              }\r\n              size=\"icon\"\r\n              className={\r\n                message.trim() || selectedFiles.length > 0\r\n                  ? \"bg-blue-500 hover:bg-blue-600 text-white rounded-full h-9 w-9 ml-2\"\r\n                  : \"text-gray-400 rounded-full h-9 w-9 ml-2\"\r\n              }\r\n              onClick={handleSendMessage}\r\n              disabled={\r\n                disabled || (!message.trim() && selectedFiles.length === 0)\r\n              }\r\n            >\r\n              <Send className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AA5BA;;;;;;;;;;;AAsCe,SAAS,aAAa,EACnC,aAAa,EACb,WAAW,KAAK,EAChB,UAAU,EACV,aAAa,EACK;;IAClB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE3C,EAAE;IACJ,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IACjD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAClD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACjD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,sBAAsB,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;0DACrC,CAAC,QAAU,MAAM,mBAAmB;;IAEtC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;+CAAE,CAAC,QAAU,MAAM,QAAQ;;IACvD,MAAM,kBAAkB,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;sDAAE,CAAC,QAAU,MAAM,eAAe;;IAErE,sDAAsD;IACtD,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,uBAAuB;QAC3B,MAAM,WAAW,YAAY,OAAO;QACpC,IAAI,UAAU;YACZ,SAAS,KAAK,CAAC,MAAM,GAAG;YACxB,MAAM,YAAY,KAAK,GAAG,CAAC,SAAS,YAAY,EAAE,MAAM,qDAAqD;YAC7G,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;QAC1C;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC1C,6CAA6C;YAC7C,MAAM,YAAY,SAAS,MAAM,CAAC,CAAC,OAAS,WAAW;YAEvD,IAAI,UAAU,MAAM,KAAK,SAAS,MAAM,EAAE;gBACxC,MAAM,gBAAgB,SAAS,MAAM,GAAG,UAAU,MAAM;gBACxD,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,GAAG,cAAc,2CAA2C,CAAC,EAC7D;oBACE,aACE;oBACF,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;gBAC/B;YAEJ;YAEA,SAAS;QACX;IACF;IAEA,qCAAqC;IACrC,MAAM,aAAa,CAAC;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,yCAAyC;QACnF,MAAM,gBAAgB,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB,IAAI;QAE3E,wCAAwC;QACxC,IAAI,aAAa,WAAW,aAAa,WAAW,aAAa,SAAS;YACxE,OAAO;QACT;QAEA,0CAA0C;QAC1C,IAAI,kBAAkB,QAAQ,CAAC,gBAAgB;YAC7C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,yFAAyF;IACzF,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE,CAAC;YAC5B;sDAAiB,CAAC,OAAS;2BAAI;2BAAS;qBAAS;;YAEjD,gCAAgC;YAChC,SAAS,OAAO;sDAAC,CAAC;oBAChB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,kCAAkC;oBAE5E,IAAI,aAAa,SAAS;wBACxB,MAAM,MAAM,IAAI,eAAe,CAAC;wBAChC;kEAAe,CAAC,OAAS;uCAAI;oCAAM;wCAAE;wCAAM;wCAAK,MAAM;oCAAQ;iCAAE;;oBAClE,OAAO,IAAI,aAAa,SAAS;wBAC/B,MAAM,MAAM,IAAI,eAAe,CAAC;wBAChC;kEAAe,CAAC,OAAS;uCAAI;oCAAM;wCAAE;wCAAM;wCAAK,MAAM;oCAAQ;iCAAE;;oBAClE,OAAO,IAAI,aAAa,SAAS;wBAC/B,MAAM,MAAM,IAAI,eAAe,CAAC;wBAChC;kEAAe,CAAC,OAAS;uCAAI;oCAAM;wCAAE;wCAAM;wCAAK,MAAM;oCAAQ;iCAAE;;oBAClE,OAAO;wBACL,yCAAyC;wBACzC;kEAAe,CAAC,OAAS;uCAAI;oCAAM;wCAAE;wCAAM,KAAK;wCAAI,MAAM;oCAAO;iCAAE;;oBACrE;gBACF;;QACF;6CAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;IAChB;IAEA,MAAM,aAAa,CACjB,GACA,kBAAkB,KAAK;QAEvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAChD,uBAAuB;YACvB,MAAM,YAAY,SAAS,MAAM,CAAC,CAAC,OAAS,WAAW;YAEvD,IAAI,UAAU,MAAM,KAAK,SAAS,MAAM,EAAE;gBACxC,MAAM,gBAAgB,SAAS,MAAM,GAAG,UAAU,MAAM;gBACxD,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,GAAG,cAAc,2CAA2C,CAAC,EAC7D;oBACE,aACE;oBACF,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;gBAC/B;YAEJ;YAEA,IAAI,iBAAiB;gBACnB,oDAAoD;gBACpD,cAAc,IAAI;YACpB,OAAO;gBACL,uCAAuC;gBACvC,SAAS;YACX;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,OAAS,SAAS;QAC1D,eAAe,CAAC;YACd,MAAM,WAAW,KAAK,MAAM,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;YACrD,0CAA0C;YAC1C,MAAM,eAAe,KAAK,IAAI,CAAC,CAAC,OAAS,KAAK,IAAI,KAAK;YACvD,IAAI,gBAAgB,aAAa,GAAG,EAAE;gBACpC,IAAI,eAAe,CAAC,aAAa,GAAG;YACtC;YACA,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,MAAM,uBAAuB;QAC3B,yBAAyB;QACzB,YAAY,OAAO,CAAC,CAAC;YACnB,IAAI,KAAK,GAAG,EAAE,IAAI,eAAe,CAAC,KAAK,GAAG;QAC5C;QACA,iBAAiB,EAAE;QACnB,eAAe,EAAE;IACnB;IAEA,6CAA6C;IAC7C,MAAM,yBAAyB;QAC7B,cAAc,OAAO,EAAE;IACzB;IAEA,uCAAuC;IACvC,MAAM,yBAAyB;QAC7B,cAAc,OAAO,EAAE;IACzB;IAEA,uCAAuC;IACvC,MAAM,4BAA4B;QAChC,iBAAiB,OAAO,EAAE;IAC5B;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,IAAI,MAAM,cAAc,MAAM,GAAG,CAAC,KAAK,CAAC,UAAU;YAC7D,6CAA6C;YAC7C,IAAI,YAAY,qBAAqB;gBACnC,YAAY;gBACZ,oBAAoB;gBAEpB,IAAI,iBAAiB,OAAO,EAAE;oBAC5B,aAAa,iBAAiB,OAAO;oBACrC,iBAAiB,OAAO,GAAG;gBAC7B;YACF;YAEA,cACE,SACA,cAAc,MAAM,GAAG,IAAI,gBAAgB;YAE7C,WAAW;YACX,qBAAqB;YACrB,wBAAwB,4BAA4B;QACtD;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,WAAW,EAAE,MAAM,CAAC,KAAK;QACzB,wEAAwE;QACxE,IAAI,mBAAmB;YACrB,qBAAqB;QACvB;QACA,iDAAiD;QACjD,WAAW,sBAAsB;QACjC,wBAAwB;QACxB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,+EAA+E;YAC/E,IAAI,aAAa;gBACf,EAAE,cAAc;gBAChB;YACF;YAEA,EAAE,cAAc;YAChB;QACF;IACF;IAEA,0BAA0B;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC/B,IAAI,CAAC,YAAY,qBAAqB;gBACpC,YAAY;gBACZ,oBAAoB;YACtB;YAEA,yBAAyB;YACzB,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,yEAAyE;YACzE,iBAAiB,OAAO,GAAG;0DAAW;oBACpC,IAAI,YAAY,qBAAqB;wBACnC,YAAY;wBACZ,oBAAoB;oBACtB;gBACF;yDAAG;QACL;iDAAG;QAAC;QAAU;KAAoB;IAElC,MAAM,mBAAmB,CAAC;QACxB,WAAW,CAAC,OAAS,OAAO,UAAU,KAAK;IAC7C;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;6DAAqB,CAAC;oBAC1B,IACE,eAAe,OAAO,IACtB,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KAC7C,eAAe,OAAO,IACtB,CAAC,eAAe,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC7C;wBACA,mBAAmB;oBACrB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;0CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;iCAAG,EAAE;IAEL,gFAAgF;IAChF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAQ;IAEZ,kDAAkD;IAClD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAC5B,CAAC;YACC,IAAI,UAAU;YAEd,MAAM,QAAQ,EAAE,aAAa,EAAE;YAC/B,IAAI,CAAC,OAAO;YAEZ,IAAI,gBAAgB;YACpB,MAAM,aAAqB,EAAE;YAE7B,4CAA4C;YAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACrC,MAAM,OAAO,KAAK,CAAC,EAAE;gBAErB,gCAAgC;gBAChC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBAClC,gBAAgB;oBAChB,MAAM,OAAO,KAAK,SAAS;oBAC3B,IAAI,MAAM;wBACR,+BAA+B;wBAC/B,MAAM,YAAY,KAAK,GAAG;wBAC1B,MAAM,UAAU,IAAI,KAAK;4BAAC;yBAAK,EAAE,CAAC,WAAW,EAAE,UAAU,IAAI,CAAC,EAAE;4BAC9D,MAAM,KAAK,IAAI;4BACf,cAAc;wBAChB;wBACA,WAAW,IAAI,CAAC;oBAClB;gBACF;YACF;YAEA,+CAA+C;YAC/C,IAAI,iBAAiB,WAAW,MAAM,GAAG,GAAG;gBAC1C,EAAE,cAAc,IAAI,mCAAmC;gBACvD,SAAS;gBACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,WAAW,MAAM,CAAC,iBAAiB,CAAC;YAC/D;QACF;gDACA;QAAC;QAAU;KAAS;IAGtB,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;0CAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;wBACrC,mDAAmD;wBACnD,IAAI,YAAY,qBAAqB;4BACnC,oBAAoB;wBACtB;oBACF;gBACF;;QACF;iCAAG;QAAC;QAAU;KAAoB;IAElC,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,mCAAmC;YACnC,MAAM,kBAAkB,YAAY,OAAO;YAC3C,IAAI,iBAAiB;gBACnB,gBAAgB,gBAAgB,CAAC,SAAS;YAC5C;YAEA,gCAAgC;YAChC;0CAAO;oBACL,IAAI,iBAAiB;wBACnB,gBAAgB,mBAAmB,CAAC,SAAS;oBAC/C;gBACF;;QACF;iCAAG;QAAC;KAAY;IAEhB,0DAA0D;IAC1D,MAAM,oBAAoB,CAAC;QACzB,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;YACxB,OAAO,QAAQ,OAAO,CAAC,IAAI;QAC7B;QAEA,IAAI,QAAQ,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC7D,MAAM,aAAa,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM;YAC/C,MAAM,aAAa,QAAQ,OAAO,CAAC,KAAK,CAAC,EAAE;YAE3C,IAAI,eAAe,GAAG;gBACpB,IAAI,WAAW,IAAI,KAAK,SAAS;oBAC/B,OAAO,CAAC,WAAW,EAAE,WAAW,QAAQ,CAAC,CAAC,CAAC;gBAC7C,OAAO,IAAI,WAAW,IAAI,KAAK,SAAS;oBACtC,OAAO,CAAC,QAAQ,EAAE,WAAW,QAAQ,CAAC,CAAC,CAAC;gBAC1C,OAAO;oBACL,OAAO,CAAC,MAAM,EAAE,WAAW,QAAQ,CAAC,CAAC,CAAC;gBACxC;YACF,OAAO;gBACL,uBAAuB;gBACvB,IAAI,WAAW,IAAI,KAAK,SAAS;oBAC/B,OAAO,CAAC,CAAC,EAAE,WAAW,UAAU,CAAC;gBACnC,OAAO,IAAI,WAAW,IAAI,KAAK,SAAS;oBACtC,OAAO,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC;gBAChC,OAAO;oBACL,OAAO,CAAC,CAAC,EAAE,WAAW,KAAK,CAAC;gBAC9B;YACF;QACF;QAEA,IAAI,QAAQ,OAAO,CAAC,KAAK,EAAE;YACzB,OAAO;QACT;QAEA,IAAI,QAAQ,OAAO,CAAC,KAAK,EAAE;YACzB,OAAO;QACT;QAEA,OAAO;IACT;IAEA,oCAAoC;IACpC,MAAM,uBAAuB;QAC3B,2DAA2D;QAC3D,IAAI,CAAC,QAAQ,IAAI,MAAM,YAAY,aAAa;QAEhD,0DAA0D;QAC1D,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI;YAC9B,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,wBAAwB;gBAClC,aAAa;YACf;YACA;QACF;QAEA,eAAe;QACf,IAAI;YACF,mDAAmD;YACnD,MAAM,kBAAkB;YAExB,sDAAsD;YACtD,MAAM,iBAAiB,SACpB,KAAK,CAAC,CAAC,GACP,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,QAAQ,EAAE,iCAAiC;aAC5D,GAAG,CAAC,CAAC,IAAM,CAAC;oBACX,SAAS,EAAE,OAAO,CAAC,IAAI,IAAI;oBAC3B,MAAM,EAAE,MAAM,EAAE,OAAO,iBAAiB,KAAK,YAAY;oBACzD,UAAU,EAAE,MAAM,EAAE,MAAM;oBAC1B,YAAY,EAAE,MAAM,EAAE,UAAU,YAAY;gBAC9C,CAAC,GACA,MAAM,CAAC,CAAC,IAAM,EAAE,OAAO,CAAC,IAAI,OAAO,KAAK,uBAAuB;YAElE,oDAAoD;YACpD,WAAW;YAEX,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAChC,iBACA,eAAe,MAAM,GAAG,IAAI,iBAAiB;YAG/C,IAAI,OAAO,OAAO,IAAI,OAAO,eAAe,EAAE;gBAC5C,4DAA4D;gBAC5D,qBAAqB;gBACrB,WAAW,OAAO,eAAe;gBACjC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,+BAA+B;oBAC3C,aAAa;gBACf;gBACA,gCAAgC;gBAChC,WAAW;oBACT,YAAY,OAAO,EAAE;gBACvB,GAAG;YACL,OAAO;gBACL,iDAAiD;gBACjD,WAAW;gBACX,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;oBAC3C,aAAa,OAAO,KAAK,IAAI;gBAC/B;YACF;QACF,EAAE,OAAM;YACN,mCAAmC;YACnC,WAAW;YACX,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;gBAC3C,aAAa;YACf;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,4BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDACV,KACE,WAAW,MAAM,EAAE,UAAU,qBAAqB;;;;;;0DAGtD,6LAAC,qIAAA,CAAA,iBAAc;0DACZ,WAAW,MAAM,EAAE,UAAU,UAC1B,MAAM,GAAG,GACV,iBAAiB;;;;;;;;;;;;kDAGxB,6LAAC;wCAAK,WAAU;kDACb,WAAW,QAAQ,GAChB,6BACA,kBAAkB;;;;;;;;;;;;;;;;;;kCAI5B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMlB,YAAY,MAAM,GAAG,mBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC;gCAAgB,WAAU;;oCACxB,KAAK,IAAI,KAAK,wBACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,KAAK,GAAG;4CACb,KAAK,KAAK,IAAI,CAAC,IAAI;4CACnB,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;+CAGZ,KAAK,IAAI,KAAK,wBAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK,KAAK,GAAG;gDACb,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;+CAGlB,KAAK,IAAI,KAAK,wBAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DACb,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;;;;;;;;;;;6DAItC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DACb,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;;;;;;;;;;;;kDAIxC,6LAAC;wCACC,WAAU;wCACV,SAAS,IAAM,iBAAiB,KAAK,IAAI;kDAEzC,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;+BAxCP;;;;;;;;;;oBA6Cb,YAAY,MAAM,GAAG,mBACpB,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC,6MAAA,CAAA,SAAM;;;;;;;;;;;;;;;;0BAMf,6LAAC;gBACC,WAAU;gBACV,YAAY;gBACZ,aAAa;gBACb,QAAQ,CAAC,IAAM,WAAW;;kCAG1B,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,QAAO;;;;;;kCAET,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,QAAO;;;;;;kCAET,6LAAC;wBACC,MAAK;wBACL,KAAK;wBACL,WAAU;wBACV,UAAU;wBACV,QAAQ;wBACR,QAAO;;;;;;kCAIT,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,6LAAC,2MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAGvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAGnB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,6LAAC,+MAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAGvB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,SAAS,IAAM,eAAe;gCAC9B,OAAM;0CAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGjB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU,YAAY,CAAC,QAAQ,IAAI,MAAM;gCACzC,SAAS;gCACT,OACE,QAAQ,IAAI,GAAG,MAAM,GAAG,KACpB,gDACA;gCAEN,WAAW,cAAc,kBAAkB;0CAE1C,4BACC,6LAAC,oNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;yDAEnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAMzB,6BACC,6LAAC,8IAAA,CAAA,UAAa;wBACZ,QAAQ,CAAC,WAAW;4BAClB,QAAQ,GAAG,CACT,sCACA,WACA,aACA;4BAGF,kDAAkD;4BAClD,MAAM,YAAY,KAAK,GAAG;4BAC1B,MAAM,WAAW,WACb,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,SAAS,IAAI,CAAC,GACrD,CAAC,cAAc,EAAE,UAAU,IAAI,CAAC;4BAEpC,qDAAqD;4BACrD,MAAM,YAAY,IAAI,KAAK;gCAAC;6BAAU,EAAE,UAAU;gCAChD,MAAM,UAAU,IAAI,IAAI;gCACxB,cAAc;4BAChB;4BAEA,gDAAgD;4BAChD,IAAI,UAAU;gCACZ,OAAO,cAAc,CAAC,WAAW,YAAY;oCAC3C,OAAO;oCACP,UAAU;gCACZ;4BACF;4BAEA,QAAQ,GAAG,CACT,uBACA,WACA,kBACA;4BAGF,sBAAsB;4BACtB,cAAc,IAAI;gCAAC;6BAAU;4BAC7B,eAAe;wBACjB;wBACA,UAAU,IAAM,eAAe;;;;;;oBAKlC,CAAC,6BACA,6LAAC;wBACC,WAAW,CAAC,sBAAsB,EAAE,aAAa,+DAA+D,IAAI;wBACpH,KAAK;;0CAEL,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK;wCACL,aACE,WAAW,6BAA6B;wCAE1C,WAAU;wCACV,OAAO;wCACP,UAAU;wCACV,WAAW;wCACX,UAAU;wCACV,MAAM;wCACN,OAAO;4CAAE,QAAQ,UAAU,SAAS;wCAAO;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAW,CAAC,qBAAqB,EAAE,kBAAkB,kBAAkB,gBAAgB,yCAAyC,CAAC;4CACjI,UAAU;4CACV,SAAS,IAAM,mBAAmB,CAAC;4CACnC,KAAK;sDAEL,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;oCAIpB,iCACC,6LAAC;wCACC,WAAU;wCACV,KAAK;kDAEL,cAAA,6LAAC,sLAAA,CAAA,UAAW;4CACV,cAAc;4CACd,OAAO;4CACP,QAAQ;4CACR,mBAAkB;4CAClB,eAAe;gDAAE,aAAa;4CAAM;4CACpC,gBAAgB;4CAChB,iBAAiB;;;;;;;;;;;;;;;;;0CAOzB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SACE,QAAQ,IAAI,MAAM,cAAc,MAAM,GAAG,IAAI,YAAY;gCAE3D,MAAK;gCACL,WACE,QAAQ,IAAI,MAAM,cAAc,MAAM,GAAG,IACrC,uEACA;gCAEN,SAAS;gCACT,UACE,YAAa,CAAC,QAAQ,IAAI,MAAM,cAAc,MAAM,KAAK;0CAG3D,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAryBwB;;QAyBM,6HAAA,CAAA,eAAY;QAGvB,6HAAA,CAAA,eAAY;QACL,6HAAA,CAAA,eAAY;;;KA7Bd", "debugId": null}}, {"offset": {"line": 4969, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ChatMessagesDropZone.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, DragEvent, useState, useRef, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { AlertCircle } from \"lucide-react\";\r\n\r\ninterface ChatMessagesDropZoneProps {\r\n  children: ReactNode;\r\n  onFileDrop: (files: File[]) => void;\r\n}\r\n\r\nexport default function ChatMessagesDropZone({\r\n  children,\r\n  onFileDrop,\r\n}: ChatMessagesDropZoneProps) {\r\n  const [isDraggingOver, setIsDraggingOver] = useState(false);\r\n\r\n  // Danh sách các định dạng file an toàn được chấp nhận\r\n  const safeDocumentTypes = [\r\n    \".pdf\",\r\n    \".doc\",\r\n    \".docx\",\r\n    \".xls\",\r\n    \".xlsx\",\r\n    \".ppt\",\r\n    \".pptx\",\r\n    \".txt\",\r\n    \".csv\",\r\n    \".rtf\",\r\n    \".odt\",\r\n    \".ods\",\r\n    \".odp\",\r\n  ];\r\n\r\n  // Kiểm tra xem file có an toàn không\r\n  const isSafeFile = (file: File): boolean => {\r\n    const fileType = file.type.split(\"/\")[0]; // image, video, audio, application, etc.\r\n    const fileExtension = `.${file.name.split(\".\").pop()?.toLowerCase() || \"\"}`;\r\n\r\n    // Chấp nhận hình ảnh, video và âm thanh\r\n    if (fileType === \"image\" || fileType === \"video\" || fileType === \"audio\") {\r\n      return true;\r\n    }\r\n\r\n    // Kiểm tra các định dạng tài liệu an toàn\r\n    if (safeDocumentTypes.includes(fileExtension)) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  // Sử dụng biến đếm để tránh hiệu ứng giật khi di chuyển chuột qua các phần tử con\r\n  const dragCounter = useRef(0);\r\n\r\n  // Reset trạng thái khi component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      dragCounter.current = 0;\r\n      setIsDraggingOver(false);\r\n    };\r\n  }, []);\r\n\r\n  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n  };\r\n\r\n  const handleDragEnter = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    dragCounter.current++;\r\n    if (dragCounter.current === 1) {\r\n      setIsDraggingOver(true);\r\n    }\r\n  };\r\n\r\n  const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    dragCounter.current--;\r\n    if (dragCounter.current === 0) {\r\n      setIsDraggingOver(false);\r\n    }\r\n  };\r\n\r\n  const handleDrop = (e: DragEvent<HTMLDivElement>) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    dragCounter.current = 0;\r\n    setIsDraggingOver(false);\r\n\r\n    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {\r\n      const allFiles = Array.from(e.dataTransfer.files);\r\n\r\n      // Lọc các file an toàn\r\n      const safeFiles = allFiles.filter((file) => isSafeFile(file));\r\n\r\n      // Hiển thị thông báo nếu có file bị từ chối\r\n      if (safeFiles.length !== allFiles.length) {\r\n        const rejectedCount = allFiles.length - safeFiles.length;\r\n        toast.error(\r\n          `${rejectedCount} file không được chấp nhận do không an toàn`,\r\n          {\r\n            description:\r\n              \"Chỉ chấp nhận hình ảnh, video, âm thanh và các tài liệu văn phòng phổ biến\",\r\n            icon: <AlertCircle className=\"h-5 w-5\" />,\r\n          },\r\n        );\r\n      }\r\n\r\n      // Chỉ gửi các file an toàn\r\n      if (safeFiles.length > 0) {\r\n        onFileDrop(safeFiles);\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className=\"relative flex-1 overflow-y-auto\"\r\n      onDragOver={handleDragOver}\r\n      onDragEnter={handleDragEnter}\r\n      onDragLeave={handleDragLeave}\r\n      onDrop={handleDrop}\r\n    >\r\n      {/* Sử dụng CSS transition để hiệu ứng mượt mà hơn */}\r\n      <div\r\n        className={`absolute inset-0 bg-blue-100/50 border-2 border-blue-300 flex items-center justify-center z-10 pointer-events-none transition-opacity duration-300 ${isDraggingOver ? \"opacity-100\" : \"opacity-0\"}`}\r\n        style={{ visibility: isDraggingOver ? \"visible\" : \"hidden\" }}\r\n      >\r\n        <div className=\"bg-white p-4 rounded-lg shadow-lg\">\r\n          <p className=\"text-blue-600 font-medium\">Thả để gửi ngay lập tức</p>\r\n          <p className=\"text-gray-500 text-sm mt-1\">\r\n            Chỉ chấp nhận hình ảnh, video, âm thanh và tài liệu an toàn\r\n          </p>\r\n        </div>\r\n      </div>\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWe,SAAS,qBAAqB,EAC3C,QAAQ,EACR,UAAU,EACgB;;IAC1B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,sDAAsD;IACtD,MAAM,oBAAoB;QACxB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qCAAqC;IACrC,MAAM,aAAa,CAAC;QAClB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,yCAAyC;QACnF,MAAM,gBAAgB,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB,IAAI;QAE3E,wCAAwC;QACxC,IAAI,aAAa,WAAW,aAAa,WAAW,aAAa,SAAS;YACxE,OAAO;QACT;QAEA,0CAA0C;QAC1C,IAAI,kBAAkB,QAAQ,CAAC,gBAAgB;YAC7C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,kFAAkF;IAClF,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE3B,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;kDAAO;oBACL,YAAY,OAAO,GAAG;oBACtB,kBAAkB;gBACpB;;QACF;yCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,EAAE,eAAe;IACnB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,YAAY,OAAO;QACnB,IAAI,YAAY,OAAO,KAAK,GAAG;YAC7B,kBAAkB;QACpB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,YAAY,OAAO;QACnB,IAAI,YAAY,OAAO,KAAK,GAAG;YAC7B,kBAAkB;QACpB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,YAAY,OAAO,GAAG;QACtB,kBAAkB;QAElB,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;YAC3D,MAAM,WAAW,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;YAEhD,uBAAuB;YACvB,MAAM,YAAY,SAAS,MAAM,CAAC,CAAC,OAAS,WAAW;YAEvD,4CAA4C;YAC5C,IAAI,UAAU,MAAM,KAAK,SAAS,MAAM,EAAE;gBACxC,MAAM,gBAAgB,SAAS,MAAM,GAAG,UAAU,MAAM;gBACxD,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,GAAG,cAAc,2CAA2C,CAAC,EAC7D;oBACE,aACE;oBACF,oBAAM,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;gBAC/B;YAEJ;YAEA,2BAA2B;YAC3B,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,WAAW;YACb;QACF;IACF;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,YAAY;QACZ,aAAa;QACb,aAAa;QACb,QAAQ;;0BAGR,6LAAC;gBACC,WAAW,CAAC,mJAAmJ,EAAE,iBAAiB,gBAAgB,aAAa;gBAC/M,OAAO;oBAAE,YAAY,iBAAiB,YAAY;gBAAS;0BAE3D,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA4B;;;;;;sCACzC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;;;;;;YAK7C;;;;;;;AAGP;GAlIwB;KAAA", "debugId": null}}, {"offset": {"line": 5140, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/TypingIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { getUserInitials } from \"@/utils/userUtils\";\r\nimport { Group, User, UserInfo } from \"@/types/base\";\r\n\r\ninterface TypingIndicatorProps {\r\n  contact?: (User & { userInfo: UserInfo }) | null;\r\n  group?: Group | null;\r\n  isTyping: boolean;\r\n  typingUsers?: Array<{\r\n    userId: string;\r\n    fullName: string;\r\n    profilePictureUrl?: string | null;\r\n    timestamp: Date;\r\n  }>;\r\n}\r\n\r\nexport default function TypingIndicator({\r\n  contact,\r\n  group,\r\n  isTyping,\r\n  typingUsers,\r\n}: TypingIndicatorProps) {\r\n  // Nếu không có ai đang nhập hoặc không có thông tin liên hệ/nhóm\r\n  if (!isTyping || (!contact && !group)) return null;\r\n\r\n  // Nếu là nhóm và có danh sách người đang nhập\r\n  if (group && typingUsers && typingUsers.length > 0) {\r\n    const firstUser = typingUsers[0];\r\n\r\n    // Đảm bảo có tên người dùng hợp lệ\r\n    const displayName =\r\n      firstUser.fullName && firstUser.fullName.trim()\r\n        ? firstUser.fullName\r\n        : \"Thành viên nhóm\";\r\n\r\n    // Log for debugging\r\n    console.log(\r\n      `[TypingIndicator] Showing typing indicator for group member: ${displayName} (${firstUser.userId})`,\r\n    );\r\n    console.log(`[TypingIndicator] Typing users:`, typingUsers);\r\n\r\n    return (\r\n      <div className=\"flex items-start gap-2 mb-2 px-3\">\r\n        <Avatar className=\"h-8 w-8 mt-1\">\r\n          <AvatarImage\r\n            src={firstUser.profilePictureUrl || \"\"}\r\n            className=\"object-cover\"\r\n          />\r\n          <AvatarFallback>{displayName.charAt(0).toUpperCase()}</AvatarFallback>\r\n        </Avatar>\r\n        <div className=\"bg-gray-200 text-gray-800 rounded-2xl px-3 py-2 text-sm flex items-center\">\r\n          <span className=\"mr-1\">\r\n            <span className=\"font-medium\">{displayName}</span> đang nhập\r\n            {typingUsers.length > 1 &&\r\n              ` và ${typingUsers.length - 1} người khác`}\r\n          </span>\r\n          <span className=\"flex\">\r\n            <span className=\"animate-bounce mx-0.5 delay-0\">.</span>\r\n            <span className=\"animate-bounce mx-0.5 delay-100\">.</span>\r\n            <span className=\"animate-bounce mx-0.5 delay-200\">.</span>\r\n          </span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Nếu là cuộc trò chuyện cá nhân\r\n  return (\r\n    <div className=\"flex items-start gap-2 mb-2 px-3\">\r\n      <Avatar className=\"h-8 w-8 mt-1\">\r\n        <AvatarImage\r\n          src={contact?.userInfo?.profilePictureUrl || undefined}\r\n          className=\"object-cover\"\r\n        />\r\n        <AvatarFallback>\r\n          {contact ? getUserInitials(contact) : \"?\"}\r\n        </AvatarFallback>\r\n      </Avatar>\r\n      <div className=\"bg-gray-200 text-gray-800 rounded-2xl px-3 py-2 text-sm flex items-center\">\r\n        <span className=\"mr-1\">Đang nhập</span>\r\n        <span className=\"flex\">\r\n          <span className=\"animate-bounce mx-0.5 delay-0\">.</span>\r\n          <span className=\"animate-bounce mx-0.5 delay-100\">.</span>\r\n          <span className=\"animate-bounce mx-0.5 delay-200\">.</span>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAkBe,SAAS,gBAAgB,EACtC,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACU;IACrB,iEAAiE;IACjE,IAAI,CAAC,YAAa,CAAC,WAAW,CAAC,OAAQ,OAAO;IAE9C,8CAA8C;IAC9C,IAAI,SAAS,eAAe,YAAY,MAAM,GAAG,GAAG;QAClD,MAAM,YAAY,WAAW,CAAC,EAAE;QAEhC,mCAAmC;QACnC,MAAM,cACJ,UAAU,QAAQ,IAAI,UAAU,QAAQ,CAAC,IAAI,KACzC,UAAU,QAAQ,GAClB;QAEN,oBAAoB;QACpB,QAAQ,GAAG,CACT,CAAC,6DAA6D,EAAE,YAAY,EAAE,EAAE,UAAU,MAAM,CAAC,CAAC,CAAC;QAErG,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC,EAAE;QAE/C,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qIAAA,CAAA,SAAM;oBAAC,WAAU;;sCAChB,6LAAC,qIAAA,CAAA,cAAW;4BACV,KAAK,UAAU,iBAAiB,IAAI;4BACpC,WAAU;;;;;;sCAEZ,6LAAC,qIAAA,CAAA,iBAAc;sCAAE,YAAY,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;8BAEpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAK,WAAU;8CAAe;;;;;;gCAAmB;gCACjD,YAAY,MAAM,GAAG,KACpB,CAAC,IAAI,EAAE,YAAY,MAAM,GAAG,EAAE,WAAW,CAAC;;;;;;;sCAE9C,6LAAC;4BAAK,WAAU;;8CACd,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;8CAClD,6LAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;;;;;;;;;;;;;IAK5D;IAEA,iCAAiC;IACjC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,SAAM;gBAAC,WAAU;;kCAChB,6LAAC,qIAAA,CAAA,cAAW;wBACV,KAAK,SAAS,UAAU,qBAAqB;wBAC7C,WAAU;;;;;;kCAEZ,6LAAC,qIAAA,CAAA,iBAAc;kCACZ,UAAU,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;;;;;;;;;;;;0BAG1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAO;;;;;;kCACvB,6LAAC;wBAAK,WAAU;;0CACd,6LAAC;gCAAK,WAAU;0CAAgC;;;;;;0CAChD,6LAAC;gCAAK,WAAU;0CAAkC;;;;;;0CAClD,6LAAC;gCAAK,WAAU;0CAAkC;;;;;;;;;;;;;;;;;;;;;;;;AAK5D;KAxEwB", "debugId": null}}, {"offset": {"line": 5353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ChatArea.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useRef, useState, useCallback } from \"react\";\r\nimport { Message, User, UserInfo, GroupMember, GroupRole } from \"@/types/base\";\r\nimport ChatHeader from \"./ChatHeader\";\r\nimport GroupChatHeader from \"./GroupChatHeader\";\r\nimport MessageItem from \"./MessageItem\";\r\nimport MessageInput from \"./MessageInput\";\r\nimport MessageDetailDialog from \"./MessageDetailDialog\";\r\nimport ChatMessagesDropZone from \"./ChatMessagesDropZone\";\r\nimport TypingIndicator from \"./TypingIndicator\";\r\nimport { formatMessageDate } from \"@/utils/dateUtils\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useConversationsStore } from \"@/stores/conversationsStore\";\r\nimport { useNotificationStore } from \"@/stores/notificationStore\";\r\nimport { getUserDataById } from \"@/actions/user.action\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { X } from \"lucide-react\";\r\n\r\ninterface ChatAreaProps {\r\n  currentUser: User;\r\n  onToggleInfo: () => void;\r\n  onBackToList?: () => void;\r\n}\r\n\r\nexport default function ChatArea({\r\n  currentUser,\r\n  onToggleInfo,\r\n  onBackToList,\r\n}: ChatAreaProps) {\r\n  const {\r\n    messages,\r\n    selectedContact,\r\n    selectedGroup,\r\n    currentChatType,\r\n    replyingTo,\r\n    selectedMessage,\r\n    isDialogOpen,\r\n    searchText,\r\n    searchResults,\r\n    isSearching,\r\n    isLoading,\r\n    isLoadingOlder,\r\n    hasMoreMessages,\r\n    clearSearch,\r\n    setReplyingTo,\r\n    setSelectedMessage,\r\n    setIsDialogOpen,\r\n    sendMessage,\r\n    loadOlderMessages,\r\n  } = useChatStore();\r\n\r\n  const { updateLastMessage, conversations } = useConversationsStore();\r\n  // Tạm thời bỏ qua markAsRead để tránh vòng lặp vô hạn\r\n  // const { markAsRead, updateLastMessage, conversations } = useConversationsStore();\r\n  const { resetUnread } = useNotificationStore();\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const chatContainerRef = useRef<HTMLDivElement>(null);\r\n  const [isTyping, setIsTyping] = useState(false);\r\n\r\n  // Keep track of previous messages to detect what changed\r\n  const prevMessagesRef = useRef<Message[]>([]);\r\n\r\n  // Use a ref to track the last message count to avoid unnecessary scrolling\r\n  const lastMessageCountRef = useRef<number>(0);\r\n  // Use a ref to track the last conversation ID\r\n  const lastConversationIdRef = useRef<string | null>(null);\r\n  // Use a ref to track if we've already scrolled for this conversation\r\n  const hasScrolledForConversationRef = useRef<boolean>(false);\r\n\r\n  // Function to scroll to bottom - extracted to avoid creating in render\r\n  const scrollToBottom = useCallback((behavior: ScrollBehavior = \"auto\") => {\r\n    if (messagesEndRef.current) {\r\n      messagesEndRef.current.scrollIntoView({ behavior });\r\n    }\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    // Skip if no messages or no selected conversation\r\n    if (!messages.length || (!selectedContact && !selectedGroup)) {\r\n      prevMessagesRef.current = [];\r\n      return;\r\n    }\r\n\r\n    const conversationId =\r\n      currentChatType === \"USER\" ? selectedContact?.id : selectedGroup?.id;\r\n\r\n    console.log(\r\n      `[ChatArea] Messages updated for ${currentChatType}: ${conversationId}, count: ${messages.length}`,\r\n    );\r\n\r\n    // Log the last few messages for debugging\r\n    if (messages.length > 0) {\r\n      const lastMessages = messages.slice(-3).map((msg) => ({\r\n        id: msg.id,\r\n        content: msg.content?.text || \"No text content\",\r\n        senderId: msg.senderId,\r\n        messageType: msg.messageType,\r\n        groupId: msg.groupId,\r\n        receiverId: msg.receiverId,\r\n        createdAt: msg.createdAt,\r\n      }));\r\n      console.log(`[ChatArea] Last messages:`, lastMessages);\r\n    }\r\n\r\n    // Only scroll to bottom when a new message is added, not when reactions change\r\n    const shouldScrollToBottom = () => {\r\n      // If message count changed, it's a new message\r\n      if (prevMessagesRef.current.length !== messages.length) {\r\n        return true;\r\n      }\r\n\r\n      // If the last message ID changed, it's a new message\r\n      if (messages.length > 0 && prevMessagesRef.current.length > 0) {\r\n        const lastMessageId = messages[messages.length - 1].id;\r\n        const prevLastMessageId =\r\n          prevMessagesRef.current[prevMessagesRef.current.length - 1].id;\r\n        return lastMessageId !== prevLastMessageId;\r\n      }\r\n\r\n      return false;\r\n    };\r\n\r\n    // Only scroll if it's a new message, not a reaction update\r\n    if (shouldScrollToBottom()) {\r\n      console.log(`[ChatArea] Scrolling to bottom for new message`);\r\n      // Use requestAnimationFrame for smoother scrolling\r\n      requestAnimationFrame(() => {\r\n        // Use smooth behavior for new messages\r\n        scrollToBottom(\"smooth\");\r\n      });\r\n    }\r\n\r\n    // Update last message in conversations list when messages change\r\n    if (messages.length > 0) {\r\n      const lastMessage = messages[messages.length - 1];\r\n      // Make sure we're updating the correct conversation\r\n      const currentState = useChatStore.getState();\r\n\r\n      if (\r\n        currentChatType === \"USER\" &&\r\n        currentState.currentChatType === \"USER\" &&\r\n        currentState.selectedContact?.id === selectedContact?.id\r\n      ) {\r\n        updateLastMessage(selectedContact!.id, lastMessage);\r\n      } else if (\r\n        currentChatType === \"GROUP\" &&\r\n        currentState.currentChatType === \"GROUP\" &&\r\n        currentState.selectedGroup?.id === selectedGroup?.id\r\n      ) {\r\n        // For groups, we need to update the conversation differently\r\n        const conversationsStore = useConversationsStore.getState();\r\n        conversationsStore.updateConversation(selectedGroup!.id, {\r\n          lastMessage: lastMessage,\r\n          lastActivity: new Date(lastMessage.createdAt),\r\n        });\r\n      }\r\n    }\r\n\r\n    // Update the previous messages reference - use a shallow copy to avoid excessive memory usage\r\n    prevMessagesRef.current = messages.slice();\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    messages,\r\n    selectedContact?.id,\r\n    selectedGroup?.id,\r\n    currentChatType,\r\n    // Remove updateLastMessage, messagesEndRef, scrollToBottom from dependencies as they are stable\r\n  ]);\r\n\r\n  // Effect for scrolling when messages change\r\n  useEffect(() => {\r\n    // Skip if no messages or no selected conversation\r\n    if (!messages.length || (!selectedContact && !selectedGroup)) {\r\n      return;\r\n    }\r\n\r\n    const conversationId =\r\n      currentChatType === \"USER\" ? selectedContact?.id : selectedGroup?.id;\r\n\r\n    if (!conversationId) return;\r\n\r\n    // Check if conversation changed\r\n    const conversationChanged =\r\n      lastConversationIdRef.current !== conversationId;\r\n\r\n    // Check if message count changed\r\n    const messageCountChanged = lastMessageCountRef.current !== messages.length;\r\n\r\n    // If conversation changed, reset the scroll flag\r\n    if (conversationChanged) {\r\n      hasScrolledForConversationRef.current = false;\r\n      lastConversationIdRef.current = conversationId;\r\n    }\r\n\r\n    // Scroll in these cases:\r\n    // 1. New conversation and we haven't scrolled yet\r\n    // 2. Same conversation but message count changed\r\n    if (\r\n      (conversationChanged && !hasScrolledForConversationRef.current) ||\r\n      (!conversationChanged && messageCountChanged)\r\n    ) {\r\n      // Only log if we're actually going to scroll\r\n      console.log(\r\n        `[ChatArea] Scrolling to bottom for ${currentChatType}: ${conversationId}`,\r\n        `(conversation changed: ${conversationChanged}, messages changed: ${messageCountChanged})`,\r\n      );\r\n\r\n      // Use requestAnimationFrame instead of setTimeout for smoother scrolling\r\n      // This ensures the scroll happens after the browser has finished rendering\r\n      requestAnimationFrame(() => {\r\n        // Use auto behavior for initial load (instant scroll)\r\n        scrollToBottom(\"auto\");\r\n      });\r\n\r\n      // Update refs\r\n      hasScrolledForConversationRef.current = true;\r\n      lastMessageCountRef.current = messages.length;\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    selectedContact?.id,\r\n    selectedGroup?.id,\r\n    currentChatType,\r\n    messages.length,\r\n    // Remove scrollToBottom, selectedContact, selectedGroup from dependencies as they are stable or redundant\r\n  ]);\r\n\r\n  // Handle scroll event to load older messages\r\n  useEffect(() => {\r\n    const chatContainer = chatContainerRef.current;\r\n    if (!chatContainer || (!selectedContact && !selectedGroup)) return;\r\n\r\n    const conversationId =\r\n      currentChatType === \"USER\" ? selectedContact?.id : selectedGroup?.id;\r\n\r\n    console.log(\r\n      `[ChatArea] Setting up scroll handler for ${currentChatType}: ${conversationId}`,\r\n    );\r\n\r\n    const handleScroll = () => {\r\n      // Check if user has scrolled near the top (within 50px from top)\r\n      if (chatContainer.scrollTop < 50 && !isLoadingOlder && hasMoreMessages) {\r\n        console.log(\r\n          `[ChatArea] Near top of scroll, loading older messages for ${currentChatType}: ${conversationId}`,\r\n        );\r\n\r\n        // Save current scroll position and height\r\n        const scrollHeight = chatContainer.scrollHeight;\r\n        const scrollPosition = chatContainer.scrollTop;\r\n\r\n        // Store the current conversation ID to verify it doesn't change during loading\r\n        const currentConversationId = conversationId;\r\n\r\n        // Load older messages\r\n        loadOlderMessages().then((success) => {\r\n          if (!success) {\r\n            console.log(\r\n              `[ChatArea] Failed to load older messages or no more messages`,\r\n            );\r\n            return;\r\n          }\r\n\r\n          // After loading, restore relative scroll position\r\n          // Use requestAnimationFrame for smoother scrolling\r\n          requestAnimationFrame(() => {\r\n            // Verify the conversation hasn't changed during loading\r\n            const currentState = useChatStore.getState();\r\n            const currentId =\r\n              currentState.currentChatType === \"USER\"\r\n                ? currentState.selectedContact?.id\r\n                : currentState.selectedGroup?.id;\r\n\r\n            if (currentId !== currentConversationId) {\r\n              console.log(\r\n                `[ChatArea] Conversation changed during loading, skipping scroll adjustment`,\r\n              );\r\n              return;\r\n            }\r\n\r\n            if (chatContainerRef.current) {\r\n              // Calculate new scroll position based on height difference\r\n              const newScrollHeight = chatContainerRef.current.scrollHeight;\r\n              const heightDifference = newScrollHeight - scrollHeight;\r\n              // Use scrollTo with behavior: 'instant' for more reliable positioning\r\n              chatContainerRef.current.scrollTo({\r\n                top: scrollPosition + heightDifference,\r\n                behavior: \"auto\",\r\n              });\r\n              console.log(\r\n                `[ChatArea] Adjusted scroll position after loading older messages`,\r\n              );\r\n            }\r\n          });\r\n        });\r\n      }\r\n    };\r\n\r\n    chatContainer.addEventListener(\"scroll\", handleScroll);\r\n    return () => {\r\n      chatContainer.removeEventListener(\"scroll\", handleScroll);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    isLoadingOlder,\r\n    hasMoreMessages,\r\n    selectedContact?.id,\r\n    selectedGroup?.id,\r\n    currentChatType,\r\n    // Remove loadOlderMessages, selectedContact, selectedGroup from dependencies as they are stable or redundant\r\n  ]);\r\n\r\n  // Fetch complete user data when viewing a conversation\r\n  useEffect(() => {\r\n    // Handle user conversations\r\n    if (currentChatType === \"USER\" && selectedContact?.id) {\r\n      console.log(\r\n        `[ChatArea] Selected contact changed to: ${selectedContact.id}`,\r\n      );\r\n\r\n      // Tạm thời bỏ qua logic đánh dấu đã đọc để tránh vòng lặp vô hạn\r\n      // TODO: Cần sửa lại logic markAsRead trong conversationsStore\r\n\r\n      // Reset global unread count\r\n      resetUnread();\r\n\r\n      // Fetch complete user data to ensure we have userInfo\r\n      const fetchCompleteUserData = async () => {\r\n        try {\r\n          // Check if we already have complete user data in conversations store\r\n          const existingConversation = conversations.find(\r\n            (conv) =>\r\n              conv.type === \"USER\" && conv.contact.id === selectedContact.id,\r\n          );\r\n\r\n          // If we already have complete user data, use it instead of making an API call\r\n          if (existingConversation?.contact?.userInfo?.fullName) {\r\n            console.log(\r\n              `[ChatArea] Using existing user data for ${selectedContact.id} from conversations store`,\r\n            );\r\n            const { setSelectedContact } = useChatStore.getState();\r\n            setSelectedContact(\r\n              existingConversation.contact as User & { userInfo: UserInfo },\r\n            );\r\n            return;\r\n          }\r\n\r\n          // Otherwise, fetch from API\r\n          const result = await getUserDataById(selectedContact.id);\r\n          if (result.success && result.user) {\r\n            // Get the current selected contact to make sure it hasn't changed\r\n            const currentSelectedContact =\r\n              useChatStore.getState().selectedContact;\r\n\r\n            // Only update if the selected contact is still the same\r\n            if (currentSelectedContact?.id === selectedContact.id) {\r\n              // Update the selected contact with complete user data\r\n              const { setSelectedContact } = useChatStore.getState();\r\n              // Ensure userInfo exists\r\n              const user = result.user;\r\n              if (!user.userInfo) {\r\n                user.userInfo = {\r\n                  id: user.id,\r\n                  fullName: user.email || user.phoneNumber || \"Unknown\",\r\n                  profilePictureUrl: null,\r\n                  statusMessage: \"No status\",\r\n                  blockStrangers: false,\r\n                  createdAt: new Date(),\r\n                  updatedAt: new Date(),\r\n                  userAuth: user,\r\n                };\r\n              }\r\n              setSelectedContact(user as User & { userInfo: UserInfo });\r\n            } else {\r\n              console.log(\r\n                `[ChatArea] Selected contact changed while fetching data, skipping update`,\r\n              );\r\n            }\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching complete user data:\", error);\r\n        }\r\n      };\r\n\r\n      fetchCompleteUserData();\r\n    }\r\n    // Handle group conversations\r\n    else if (currentChatType === \"GROUP\" && selectedGroup?.id) {\r\n      console.log(`[ChatArea] Selected group changed to: ${selectedGroup.id}`);\r\n\r\n      // Tạm thời bỏ qua logic đánh dấu đã đọc để tránh vòng lặp vô hạn\r\n      // TODO: Cần sửa lại logic markAsRead trong conversationsStore\r\n\r\n      // Reset global unread count\r\n      resetUnread();\r\n\r\n      // No need to fetch user data for group members - we already have this information\r\n      // in the group object from the conversations store\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    selectedContact?.id,\r\n    selectedGroup?.id,\r\n    currentChatType,\r\n    // Remove resetUnread and conversations from dependencies as they are stable or cause infinite loops\r\n  ]);\r\n\r\n  // Track typing status from conversationsStore\r\n  // Use a ref to track the subscription\r\n  const typingSubscriptionRef = useRef<(() => void) | null>(null);\r\n  // Use a ref to track the current typing status to avoid unnecessary state updates\r\n  const currentTypingStatusRef = useRef<boolean>(false);\r\n  // Use a ref to track the current conversation ID to avoid unnecessary re-subscriptions\r\n  const typingConversationIdRef = useRef<string | null>(null);\r\n\r\n  // Function to update typing status - extracted to avoid creating in render\r\n  // Optimized to prevent infinite update loops\r\n  const updateTypingStatus = useCallback(\r\n    (newStatus: boolean) => {\r\n      // Only update state if status has changed\r\n      if (currentTypingStatusRef.current !== newStatus) {\r\n        // Update the ref first\r\n        currentTypingStatusRef.current = newStatus;\r\n\r\n        // Then update the state\r\n        setIsTyping(newStatus);\r\n\r\n        // Scroll to bottom when typing status changes to true\r\n        if (newStatus) {\r\n          // Use setTimeout with 0 delay to ensure this happens after state updates\r\n          setTimeout(() => {\r\n            scrollToBottom(\"smooth\");\r\n          }, 0);\r\n        }\r\n      }\r\n    },\r\n    [scrollToBottom],\r\n  );\r\n\r\n  // Effect for typing indicator - optimized to prevent infinite updates\r\n  useEffect(() => {\r\n    // Skip if no selected conversation\r\n    if ((!selectedContact && !selectedGroup) || !currentChatType) {\r\n      updateTypingStatus(false);\r\n      return;\r\n    }\r\n\r\n    const conversationId =\r\n      currentChatType === \"USER\" ? selectedContact?.id : selectedGroup?.id;\r\n\r\n    if (!conversationId) {\r\n      updateTypingStatus(false);\r\n      return;\r\n    }\r\n\r\n    // If the conversation hasn't changed, don't re-subscribe\r\n    if (typingConversationIdRef.current === conversationId) {\r\n      return;\r\n    }\r\n\r\n    console.log(\r\n      `[ChatArea] Setting up typing indicator for ${currentChatType}: ${conversationId}`,\r\n    );\r\n\r\n    // Update the current conversation ID\r\n    typingConversationIdRef.current = conversationId;\r\n\r\n    // Clean up previous subscription if it exists\r\n    if (typingSubscriptionRef.current) {\r\n      typingSubscriptionRef.current();\r\n      typingSubscriptionRef.current = null;\r\n    }\r\n\r\n    // Check initial typing status - use a local variable to avoid closure issues\r\n    let initialTypingStatus = false;\r\n    if (currentChatType === \"USER\") {\r\n      const conversation = conversations.find(\r\n        (conv) =>\r\n          conv.type === \"USER\" && conv.contact.id === selectedContact?.id,\r\n      );\r\n      initialTypingStatus = !!conversation?.isTyping;\r\n    } else {\r\n      const conversation = conversations.find(\r\n        (conv) => conv.type === \"GROUP\" && conv.group?.id === selectedGroup?.id,\r\n      );\r\n      initialTypingStatus = !!conversation?.isTyping;\r\n    }\r\n\r\n    // Update initial typing status\r\n    updateTypingStatus(initialTypingStatus);\r\n\r\n    // Create a stable reference to the conversation type and ID\r\n    const stableType = currentChatType;\r\n    const stableId = conversationId;\r\n\r\n    // Use a local variable to track the last typing status to avoid unnecessary updates\r\n    let lastTypingStatus = initialTypingStatus;\r\n\r\n    // Subscribe to changes\r\n    const unsubscribe = useConversationsStore.subscribe((state) => {\r\n      // Skip processing if component is unmounted or conversation changed\r\n      if (typingConversationIdRef.current !== stableId) {\r\n        return;\r\n      }\r\n\r\n      // Get the current state to make sure the conversation hasn't changed\r\n      const currentState = useChatStore.getState();\r\n      const currentId =\r\n        currentState.currentChatType === \"USER\"\r\n          ? currentState.selectedContact?.id\r\n          : currentState.selectedGroup?.id;\r\n\r\n      if (\r\n        !currentId ||\r\n        currentId !== stableId ||\r\n        currentState.currentChatType !== stableType\r\n      ) {\r\n        // If conversation has changed, don't update typing status\r\n        return;\r\n      }\r\n\r\n      // Find the updated conversation\r\n      let updatedConversation: (typeof state.conversations)[0] | undefined;\r\n      if (stableType === \"USER\") {\r\n        updatedConversation = state.conversations.find(\r\n          (conv) => conv.type === \"USER\" && conv.contact.id === stableId,\r\n        );\r\n      } else {\r\n        updatedConversation = state.conversations.find(\r\n          (conv) => conv.type === \"GROUP\" && conv.group?.id === stableId,\r\n        );\r\n      }\r\n\r\n      // Get the new typing status\r\n      const newTypingStatus = !!updatedConversation?.isTyping;\r\n\r\n      // Only update if the status has changed\r\n      if (newTypingStatus !== lastTypingStatus) {\r\n        lastTypingStatus = newTypingStatus;\r\n        // Use requestAnimationFrame to batch updates and avoid React render cycles\r\n        requestAnimationFrame(() => {\r\n          // Double-check that the conversation hasn't changed before updating\r\n          if (typingConversationIdRef.current === stableId) {\r\n            updateTypingStatus(newTypingStatus);\r\n          }\r\n        });\r\n      }\r\n    });\r\n\r\n    // Store the unsubscribe function in the ref\r\n    typingSubscriptionRef.current = unsubscribe;\r\n\r\n    return () => {\r\n      // Clean up subscription when unmounting or changing conversation\r\n      if (typingSubscriptionRef.current) {\r\n        typingSubscriptionRef.current();\r\n        typingSubscriptionRef.current = null;\r\n      }\r\n      // Reset typing status\r\n      updateTypingStatus(false);\r\n      // Reset current conversation ID\r\n      typingConversationIdRef.current = null;\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    selectedContact?.id,\r\n    selectedGroup?.id,\r\n    currentChatType,\r\n    // Remove conversations, updateTypingStatus, selectedContact, selectedGroup from dependencies\r\n    // as they cause infinite loops or are redundant\r\n  ]);\r\n\r\n  const handleReply = (message: Message) => {\r\n    setReplyingTo(message);\r\n  };\r\n\r\n  const handleMessageClick = (message: Message) => {\r\n    // Chỉ mở dialog khi tin nhắn có media\r\n    if (\r\n      message.content.media?.length ||\r\n      message.content.image ||\r\n      message.content.video\r\n    ) {\r\n      setSelectedMessage(message);\r\n      setIsDialogOpen(true);\r\n    } else if (message.content.text) {\r\n      // Nếu chỉ có text thì hiển thị toast\r\n    }\r\n  };\r\n\r\n  const handleCancelReply = () => {\r\n    setReplyingTo(null);\r\n  };\r\n\r\n  const handleSendMessage = (text: string, files?: File[]) => {\r\n    sendMessage(text, files, currentUser);\r\n  };\r\n\r\n  // Group messages by date\r\n  const groupMessagesByDate = () => {\r\n    const groups: { date: string; messages: Message[] }[] = [];\r\n    let currentDate = \"\";\r\n    let currentGroup: Message[] = [];\r\n\r\n    messages.forEach((message) => {\r\n      // Skip messages without createdAt\r\n      if (!message.createdAt) {\r\n        console.warn(\"Message without createdAt:\", message);\r\n        return;\r\n      }\r\n\r\n      // Use our utility function to format the date\r\n      const messageDate = formatMessageDate(message.createdAt);\r\n\r\n      if (messageDate !== currentDate) {\r\n        if (currentGroup.length > 0) {\r\n          groups.push({ date: currentDate, messages: currentGroup });\r\n        }\r\n        currentDate = messageDate;\r\n        currentGroup = [message];\r\n      } else {\r\n        currentGroup.push(message);\r\n      }\r\n    });\r\n\r\n    if (currentGroup.length > 0) {\r\n      groups.push({ date: currentDate, messages: currentGroup });\r\n    }\r\n\r\n    return groups;\r\n  };\r\n\r\n  // Group messages by sender (to avoid showing avatar for consecutive messages)\r\n  const processMessagesForDisplay = (messages: Message[]) => {\r\n    if (!messages || !Array.isArray(messages)) {\r\n      console.error(\r\n        \"Invalid messages array in processMessagesForDisplay\",\r\n        messages,\r\n      );\r\n      return [];\r\n    }\r\n\r\n    // Tạo một map để lưu trữ thông tin người dùng đã tìm thấy\r\n    // Điều này giúp tránh việc tìm kiếm lặp lại thông tin cho cùng một người dùng\r\n    const userInfoCache: Record<string, UserInfo | undefined> = {};\r\n\r\n    return messages\r\n      .map((message, index, array) => {\r\n        // Ensure message is valid\r\n        if (!message) {\r\n          console.error(\"Invalid message in processMessagesForDisplay\");\r\n          return {\r\n            message: {} as Message,\r\n            isCurrentUser: false,\r\n            showAvatar: false,\r\n            userInfo: undefined,\r\n          };\r\n        }\r\n\r\n        // Safely check if current user exists\r\n        const isCurrentUser = currentUser?.id\r\n          ? message.senderId === currentUser.id\r\n          : false;\r\n        const prevMessage = index > 0 ? array[index - 1] : null;\r\n        const showAvatar =\r\n          !prevMessage || prevMessage.senderId !== message.senderId;\r\n\r\n        // Nếu là người dùng hiện tại, sử dụng thông tin của họ\r\n        if (isCurrentUser && currentUser?.userInfo) {\r\n          return {\r\n            message,\r\n            isCurrentUser,\r\n            showAvatar,\r\n            userInfo: currentUser.userInfo,\r\n          };\r\n        }\r\n\r\n        // Kiểm tra xem đã có thông tin người dùng trong cache chưa\r\n        if (userInfoCache[message.senderId]) {\r\n          return {\r\n            message,\r\n            isCurrentUser,\r\n            showAvatar,\r\n            userInfo: userInfoCache[message.senderId],\r\n          };\r\n        }\r\n\r\n        // Ensure we have userInfo for the sender\r\n        let userInfo = message.sender?.userInfo;\r\n\r\n        // Nếu đã có thông tin người gửi trong tin nhắn, sử dụng nó\r\n        if (userInfo && userInfo.fullName) {\r\n          userInfoCache[message.senderId] = userInfo;\r\n          return { message, isCurrentUser, showAvatar, userInfo };\r\n        }\r\n\r\n        // For group messages, try to find sender info from the group members\r\n        if (currentChatType === \"GROUP\" && !isCurrentUser) {\r\n          // Tìm thông tin nhóm từ conversationsStore\r\n          const groupConversation = conversations?.find(\r\n            (conv) =>\r\n              conv?.type === \"GROUP\" &&\r\n              (conv?.group?.id === message.groupId ||\r\n                (selectedGroup && conv?.group?.id === selectedGroup.id)),\r\n          );\r\n\r\n          // Tìm thông tin người gửi từ danh sách thành viên nhóm\r\n          type MemberInfo = {\r\n            id: string;\r\n            fullName: string;\r\n            profilePictureUrl?: string | null;\r\n            role: GroupRole;\r\n          };\r\n\r\n          let memberInfo: MemberInfo | null = null;\r\n\r\n          // 1. Tìm trong memberUsers (cách mới)\r\n          if (groupConversation?.group?.memberUsers) {\r\n            const memberUser = groupConversation.group.memberUsers.find(\r\n              (m) => m.id === message.senderId,\r\n            );\r\n\r\n            if (memberUser) {\r\n              memberInfo = memberUser;\r\n            }\r\n          }\r\n\r\n          // 2. Nếu không tìm thấy, thử tìm trong members (cách cũ)\r\n          if (!memberInfo && groupConversation?.group) {\r\n            interface ApiGroupMember {\r\n              id: string;\r\n              userId: string;\r\n              fullName: string;\r\n              profilePictureUrl?: string | null;\r\n              role: string;\r\n            }\r\n\r\n            // Sử dụng type assertion để truy cập vào members\r\n            const apiMembers = (\r\n              groupConversation.group as unknown as {\r\n                members?: ApiGroupMember[];\r\n              }\r\n            ).members;\r\n\r\n            if (apiMembers && Array.isArray(apiMembers)) {\r\n              const member = apiMembers.find(\r\n                (m: ApiGroupMember) => m.userId === message.senderId,\r\n              );\r\n\r\n              if (member) {\r\n                memberInfo = {\r\n                  id: member.userId,\r\n                  fullName: member.fullName,\r\n                  profilePictureUrl: member.profilePictureUrl,\r\n                  role: member.role as GroupRole,\r\n                };\r\n              }\r\n            }\r\n          }\r\n\r\n          // 3. Nếu không tìm thấy, thử tìm trong selectedGroup\r\n          if (!memberInfo && selectedGroup) {\r\n            // Tìm trong memberUsers (cách mới)\r\n            if (selectedGroup.memberUsers) {\r\n              const memberUser = selectedGroup.memberUsers.find(\r\n                (m) => m.id === message.senderId,\r\n              );\r\n\r\n              if (memberUser) {\r\n                memberInfo = memberUser;\r\n              }\r\n            }\r\n\r\n            // Nếu không tìm thấy, thử tìm trong members (cách cũ)\r\n            if (!memberInfo && selectedGroup.members) {\r\n              const member = selectedGroup.members.find(\r\n                (m: GroupMember) => m.userId === message.senderId,\r\n              );\r\n\r\n              if (member) {\r\n                // Lấy thông tin từ user object của member\r\n                memberInfo = {\r\n                  id: member.userId,\r\n                  fullName: member.user?.userInfo?.fullName || \"Unknown\",\r\n                  profilePictureUrl: member.user?.userInfo?.profilePictureUrl,\r\n                  role: member.role as GroupRole,\r\n                };\r\n              }\r\n            }\r\n          }\r\n\r\n          // Nếu tìm thấy thông tin thành viên, tạo userInfo\r\n          if (memberInfo) {\r\n            userInfo = {\r\n              id: memberInfo.id,\r\n              fullName: memberInfo.fullName || \"Unknown\",\r\n              profilePictureUrl: memberInfo.profilePictureUrl,\r\n              createdAt: new Date(),\r\n              updatedAt: new Date(),\r\n              blockStrangers: false,\r\n              userAuth: { id: memberInfo.id } as User,\r\n            };\r\n\r\n            // Lưu vào cache để sử dụng lại\r\n            userInfoCache[message.senderId] = userInfo;\r\n\r\n            // Cập nhật thông tin người gửi vào message để đảm bảo tính nhất quán\r\n            if (message.sender) {\r\n              message.sender.userInfo = userInfo;\r\n            } else {\r\n              message.sender = {\r\n                id: memberInfo.id,\r\n                userInfo: userInfo,\r\n              } as User;\r\n            }\r\n          }\r\n        }\r\n\r\n        // Fallback to selected contact info for direct messages\r\n        if (\r\n          !userInfo &&\r\n          currentChatType === \"USER\" &&\r\n          selectedContact?.userInfo\r\n        ) {\r\n          userInfo = selectedContact.userInfo;\r\n          userInfoCache[message.senderId] = userInfo;\r\n        }\r\n\r\n        // Ensure userInfo is never null (only undefined is allowed by the type)\r\n        if (userInfo === null) {\r\n          userInfo = undefined;\r\n        }\r\n\r\n        return { message, isCurrentUser, showAvatar, userInfo };\r\n      })\r\n      .filter((item) => item.message && item.message.id); // Filter out invalid messages\r\n  };\r\n\r\n  const messageGroups = groupMessagesByDate();\r\n\r\n  // Process messages or search results for display\r\n  const renderMessageContent = () => {\r\n    // If we're searching, display search results\r\n    if (searchText && searchResults.length > 0) {\r\n      return (\r\n        <>\r\n          <div className=\"mb-4\">\r\n            <div className=\"sticky top-0 bg-blue-50 p-2 rounded-md flex justify-between items-center mb-4 z-10\">\r\n              <div className=\"text-sm text-blue-700\">\r\n                Kết quả tìm kiếm: {searchResults.length} tin nhắn\r\n              </div>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                className=\"h-7 px-2 text-blue-700 hover:bg-blue-100\"\r\n                onClick={clearSearch}\r\n              >\r\n                <X className=\"h-4 w-4 mr-1\" />\r\n                Đóng\r\n              </Button>\r\n            </div>\r\n\r\n            {processMessagesForDisplay(searchResults).map(\r\n              ({ message, isCurrentUser, showAvatar, userInfo }) => (\r\n                <MessageItem\r\n                  key={message.id}\r\n                  message={message}\r\n                  isCurrentUser={isCurrentUser}\r\n                  showAvatar={showAvatar}\r\n                  onReply={handleReply}\r\n                  onMessageClick={handleMessageClick}\r\n                  highlight={searchText}\r\n                  userInfo={userInfo}\r\n                  isGroupMessage={currentChatType === \"GROUP\"}\r\n                />\r\n              ),\r\n            )}\r\n          </div>\r\n\r\n          {/* Typing indicator at the bottom */}\r\n          {isTyping && (\r\n            <div className=\"mb-2\">\r\n              {currentChatType === \"USER\" && selectedContact ? (\r\n                <TypingIndicator\r\n                  contact={selectedContact}\r\n                  isTyping={isTyping}\r\n                />\r\n              ) : currentChatType === \"GROUP\" && selectedGroup ? (\r\n                <TypingIndicator\r\n                  group={selectedGroup}\r\n                  isTyping={isTyping}\r\n                  typingUsers={\r\n                    conversations.find(\r\n                      (c) =>\r\n                        c.type === \"GROUP\" && c.group?.id === selectedGroup.id,\r\n                    )?.typingUsers\r\n                  }\r\n                />\r\n              ) : null}\r\n            </div>\r\n          )}\r\n        </>\r\n      );\r\n    }\r\n\r\n    // Otherwise show normal messages grouped by date\r\n    return (\r\n      <>\r\n        {messageGroups.length > 0 ? (\r\n          messageGroups.map((group, groupIndex) => (\r\n            <div key={groupIndex} className=\"mb-4\">\r\n              <div className=\"flex justify-center mb-4\">\r\n                <div className=\"bg-white px-3 py-1 rounded-full text-xs text-gray-500 shadow-sm\">\r\n                  {group.date}\r\n                </div>\r\n              </div>\r\n\r\n              {processMessagesForDisplay(group.messages).map(\r\n                ({ message, isCurrentUser, showAvatar, userInfo }) => (\r\n                  <MessageItem\r\n                    key={message.id}\r\n                    message={message}\r\n                    isCurrentUser={isCurrentUser}\r\n                    showAvatar={showAvatar}\r\n                    onReply={handleReply}\r\n                    onMessageClick={handleMessageClick}\r\n                    userInfo={userInfo}\r\n                    isGroupMessage={currentChatType === \"GROUP\"}\r\n                  />\r\n                ),\r\n              )}\r\n            </div>\r\n          ))\r\n        ) : (\r\n          <div className=\"h-full flex items-center justify-center\">\r\n            <p className=\"text-gray-500\">\r\n              Chưa có tin nhắn nào. Hãy bắt đầu cuộc trò chuyện!\r\n            </p>\r\n          </div>\r\n        )}\r\n\r\n        {/* Typing indicator at the bottom */}\r\n        {isTyping && (\r\n          <div className=\"mb-2\">\r\n            {currentChatType === \"USER\" && selectedContact ? (\r\n              <TypingIndicator contact={selectedContact} isTyping={isTyping} />\r\n            ) : currentChatType === \"GROUP\" && selectedGroup ? (\r\n              <TypingIndicator\r\n                group={selectedGroup}\r\n                isTyping={isTyping}\r\n                typingUsers={\r\n                  conversations.find(\r\n                    (c) =>\r\n                      c.type === \"GROUP\" && c.group?.id === selectedGroup.id,\r\n                  )?.typingUsers\r\n                }\r\n              />\r\n            ) : null}\r\n          </div>\r\n        )}\r\n      </>\r\n    );\r\n  };\r\n\r\n  // Kiểm tra xem nhóm có còn tồn tại không khi selectedGroup thay đổi hoặc danh sách cuộc trò chuyện thay đổi\r\n  // Optimized to prevent infinite update loops\r\n  useEffect(() => {\r\n    // Skip if no group selected\r\n    if (currentChatType !== \"GROUP\" || !selectedGroup || !selectedGroup.id) {\r\n      return;\r\n    }\r\n\r\n    // Store the current group ID to avoid closure issues\r\n    const currentGroupId = selectedGroup.id;\r\n\r\n    // Use setTimeout to ensure this check happens after other state updates\r\n    const checkGroupExistence = setTimeout(() => {\r\n      // Get the latest conversations state\r\n      const currentConversations =\r\n        useConversationsStore.getState().conversations;\r\n\r\n      // Check if the group still exists\r\n      const groupExists = currentConversations.some(\r\n        (conv) => conv.type === \"GROUP\" && conv.group?.id === currentGroupId,\r\n      );\r\n\r\n      // If group doesn't exist, close the chat\r\n      if (!groupExists) {\r\n        console.log(\r\n          `[ChatArea] Group ${currentGroupId} no longer exists in conversations, closing chat`,\r\n        );\r\n        // Get the current chat state to ensure we're still looking at the same group\r\n        const currentChatState = useChatStore.getState();\r\n        if (currentChatState.selectedGroup?.id === currentGroupId) {\r\n          useChatStore.getState().setSelectedGroup(null);\r\n        }\r\n      }\r\n    }, 100); // Small delay to avoid race conditions\r\n\r\n    // Clean up the timeout\r\n    return () => clearTimeout(checkGroupExistence);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    currentChatType,\r\n    selectedGroup?.id,\r\n    // Remove conversations and conversations.length from dependencies to prevent excessive re-renders\r\n  ]);\r\n\r\n  // Check if current user is valid - this helps prevent errors when auth state changes\r\n  const isUserValid = !!currentUser?.id;\r\n\r\n  // If user is not valid, show a message instead of the chat\r\n  if (!isUserValid) {\r\n    return (\r\n      <div className=\"flex flex-col h-full w-full items-center justify-center bg-gray-50\">\r\n        <div className=\"text-center p-4\">\r\n          <p className=\"text-gray-500 mb-2\">\r\n            Phiên đăng nhập của bạn đã hết hạn.\r\n          </p>\r\n          <p className=\"text-gray-500\">Vui lòng đăng nhập lại để tiếp tục.</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full w-full\">\r\n      {currentChatType === \"USER\" ? (\r\n        <ChatHeader\r\n          contact={selectedContact}\r\n          onToggleInfo={onToggleInfo}\r\n          onBackToList={onBackToList}\r\n        />\r\n      ) : (\r\n        <GroupChatHeader\r\n          group={selectedGroup}\r\n          onToggleInfo={onToggleInfo}\r\n          onBackToList={onBackToList}\r\n        />\r\n      )}\r\n\r\n      <ChatMessagesDropZone\r\n        onFileDrop={(files) => handleSendMessage(\"\", files)}\r\n      >\r\n        <div\r\n          ref={chatContainerRef}\r\n          className=\"overflow-y-auto overflow-x-hidden bg-gray-50 p-4 custom-scrollbar h-full\"\r\n        >\r\n          {selectedContact || selectedGroup ? (\r\n            isLoading ? (\r\n              <div className=\"h-full flex items-center justify-center\">\r\n                <div className=\"flex flex-col items-center\">\r\n                  <div className=\"w-8 h-8 border-4 border-t-blue-500 border-b-blue-300 border-l-blue-300 border-r-blue-300 rounded-full animate-spin mb-2\"></div>\r\n                  <p className=\"text-gray-500\">Đang tải tin nhắn...</p>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {isLoadingOlder && (\r\n                  <div className=\"flex justify-center mb-4\">\r\n                    <div className=\"flex items-center text-xs text-gray-500\">\r\n                      <div className=\"w-4 h-4 border-2 border-t-blue-500 border-b-blue-300 border-l-blue-300 border-r-blue-300 rounded-full animate-spin mr-2\"></div>\r\n                      Đang tải tin nhắn cũ hơn...\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                {renderMessageContent()}\r\n              </>\r\n            )\r\n          ) : (\r\n            <div className=\"h-full flex items-center justify-center\">\r\n              <p className=\"text-gray-500\">\r\n                Chọn một liên hệ hoặc nhóm để bắt đầu trò chuyện\r\n              </p>\r\n            </div>\r\n          )}\r\n          <div ref={messagesEndRef} />\r\n        </div>\r\n      </ChatMessagesDropZone>\r\n\r\n      <MessageInput\r\n        onSendMessage={handleSendMessage}\r\n        disabled={(!selectedContact && !selectedGroup) || isSearching}\r\n        replyingTo={replyingTo}\r\n        onCancelReply={handleCancelReply}\r\n      />\r\n\r\n      {/* Dialog hiển thị chi tiết tin nhắn */}\r\n      <MessageDetailDialog\r\n        message={selectedMessage}\r\n        isOpen={isDialogOpen}\r\n        onClose={() => setIsDialogOpen(false)}\r\n        userInfo={\r\n          selectedMessage?.sender?.userInfo ||\r\n          (currentChatType === \"USER\" ? selectedContact?.userInfo : undefined)\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;;AAyBe,SAAS,SAAS,EAC/B,WAAW,EACX,YAAY,EACZ,YAAY,EACE;;IACd,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,aAAa,EACb,eAAe,EACf,UAAU,EACV,eAAe,EACf,YAAY,EACZ,UAAU,EACV,aAAa,EACb,WAAW,EACX,SAAS,EACT,cAAc,EACd,eAAe,EACf,WAAW,EACX,aAAa,EACb,kBAAkB,EAClB,eAAe,EACf,WAAW,EACX,iBAAiB,EAClB,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,wBAAqB,AAAD;IACjE,sDAAsD;IACtD,oFAAoF;IACpF,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,uBAAoB,AAAD;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAChD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yDAAyD;IACzD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAa,EAAE;IAE5C,2EAA2E;IAC3E,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAC3C,8CAA8C;IAC9C,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACpD,qEAAqE;IACrE,MAAM,gCAAgC,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAW;IAEtD,uEAAuE;IACvE,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE,CAAC,WAA2B,MAAM;YACnE,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE;gBAAS;YACnD;QACF;+CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,kDAAkD;YAClD,IAAI,CAAC,SAAS,MAAM,IAAK,CAAC,mBAAmB,CAAC,eAAgB;gBAC5D,gBAAgB,OAAO,GAAG,EAAE;gBAC5B;YACF;YAEA,MAAM,iBACJ,oBAAoB,SAAS,iBAAiB,KAAK,eAAe;YAEpE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAAE,gBAAgB,EAAE,EAAE,eAAe,SAAS,EAAE,SAAS,MAAM,EAAE;YAGpG,0CAA0C;YAC1C,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,eAAe,SAAS,KAAK,CAAC,CAAC,GAAG,GAAG;uDAAC,CAAC,MAAQ,CAAC;4BACpD,IAAI,IAAI,EAAE;4BACV,SAAS,IAAI,OAAO,EAAE,QAAQ;4BAC9B,UAAU,IAAI,QAAQ;4BACtB,aAAa,IAAI,WAAW;4BAC5B,SAAS,IAAI,OAAO;4BACpB,YAAY,IAAI,UAAU;4BAC1B,WAAW,IAAI,SAAS;wBAC1B,CAAC;;gBACD,QAAQ,GAAG,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC3C;YAEA,+EAA+E;YAC/E,MAAM;2DAAuB;oBAC3B,+CAA+C;oBAC/C,IAAI,gBAAgB,OAAO,CAAC,MAAM,KAAK,SAAS,MAAM,EAAE;wBACtD,OAAO;oBACT;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,MAAM,GAAG,KAAK,gBAAgB,OAAO,CAAC,MAAM,GAAG,GAAG;wBAC7D,MAAM,gBAAgB,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,EAAE;wBACtD,MAAM,oBACJ,gBAAgB,OAAO,CAAC,gBAAgB,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,EAAE;wBAChE,OAAO,kBAAkB;oBAC3B;oBAEA,OAAO;gBACT;;YAEA,2DAA2D;YAC3D,IAAI,wBAAwB;gBAC1B,QAAQ,GAAG,CAAC,CAAC,8CAA8C,CAAC;gBAC5D,mDAAmD;gBACnD;0CAAsB;wBACpB,uCAAuC;wBACvC,eAAe;oBACjB;;YACF;YAEA,iEAAiE;YACjE,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,MAAM,cAAc,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;gBACjD,oDAAoD;gBACpD,MAAM,eAAe,6HAAA,CAAA,eAAY,CAAC,QAAQ;gBAE1C,IACE,oBAAoB,UACpB,aAAa,eAAe,KAAK,UACjC,aAAa,eAAe,EAAE,OAAO,iBAAiB,IACtD;oBACA,kBAAkB,gBAAiB,EAAE,EAAE;gBACzC,OAAO,IACL,oBAAoB,WACpB,aAAa,eAAe,KAAK,WACjC,aAAa,aAAa,EAAE,OAAO,eAAe,IAClD;oBACA,6DAA6D;oBAC7D,MAAM,qBAAqB,sIAAA,CAAA,wBAAqB,CAAC,QAAQ;oBACzD,mBAAmB,kBAAkB,CAAC,cAAe,EAAE,EAAE;wBACvD,aAAa;wBACb,cAAc,IAAI,KAAK,YAAY,SAAS;oBAC9C;gBACF;YACF;YAEA,8FAA8F;YAC9F,gBAAgB,OAAO,GAAG,SAAS,KAAK;QACxC,uDAAuD;QACzD;6BAAG;QACD;QACA,iBAAiB;QACjB,eAAe;QACf;KAED;IAED,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,kDAAkD;YAClD,IAAI,CAAC,SAAS,MAAM,IAAK,CAAC,mBAAmB,CAAC,eAAgB;gBAC5D;YACF;YAEA,MAAM,iBACJ,oBAAoB,SAAS,iBAAiB,KAAK,eAAe;YAEpE,IAAI,CAAC,gBAAgB;YAErB,gCAAgC;YAChC,MAAM,sBACJ,sBAAsB,OAAO,KAAK;YAEpC,iCAAiC;YACjC,MAAM,sBAAsB,oBAAoB,OAAO,KAAK,SAAS,MAAM;YAE3E,iDAAiD;YACjD,IAAI,qBAAqB;gBACvB,8BAA8B,OAAO,GAAG;gBACxC,sBAAsB,OAAO,GAAG;YAClC;YAEA,yBAAyB;YACzB,kDAAkD;YAClD,iDAAiD;YACjD,IACE,AAAC,uBAAuB,CAAC,8BAA8B,OAAO,IAC7D,CAAC,uBAAuB,qBACzB;gBACA,6CAA6C;gBAC7C,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,gBAAgB,EAAE,EAAE,gBAAgB,EAC1E,CAAC,uBAAuB,EAAE,oBAAoB,oBAAoB,EAAE,oBAAoB,CAAC,CAAC;gBAG5F,yEAAyE;gBACzE,2EAA2E;gBAC3E;0CAAsB;wBACpB,sDAAsD;wBACtD,eAAe;oBACjB;;gBAEA,cAAc;gBACd,8BAA8B,OAAO,GAAG;gBACxC,oBAAoB,OAAO,GAAG,SAAS,MAAM;YAC/C;QACA,uDAAuD;QACzD;6BAAG;QACD,iBAAiB;QACjB,eAAe;QACf;QACA,SAAS,MAAM;KAEhB;IAED,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,gBAAgB,iBAAiB,OAAO;YAC9C,IAAI,CAAC,iBAAkB,CAAC,mBAAmB,CAAC,eAAgB;YAE5D,MAAM,iBACJ,oBAAoB,SAAS,iBAAiB,KAAK,eAAe;YAEpE,QAAQ,GAAG,CACT,CAAC,yCAAyC,EAAE,gBAAgB,EAAE,EAAE,gBAAgB;YAGlF,MAAM;mDAAe;oBACnB,iEAAiE;oBACjE,IAAI,cAAc,SAAS,GAAG,MAAM,CAAC,kBAAkB,iBAAiB;wBACtE,QAAQ,GAAG,CACT,CAAC,0DAA0D,EAAE,gBAAgB,EAAE,EAAE,gBAAgB;wBAGnG,0CAA0C;wBAC1C,MAAM,eAAe,cAAc,YAAY;wBAC/C,MAAM,iBAAiB,cAAc,SAAS;wBAE9C,+EAA+E;wBAC/E,MAAM,wBAAwB;wBAE9B,sBAAsB;wBACtB,oBAAoB,IAAI;+DAAC,CAAC;gCACxB,IAAI,CAAC,SAAS;oCACZ,QAAQ,GAAG,CACT,CAAC,4DAA4D,CAAC;oCAEhE;gCACF;gCAEA,kDAAkD;gCAClD,mDAAmD;gCACnD;uEAAsB;wCACpB,wDAAwD;wCACxD,MAAM,eAAe,6HAAA,CAAA,eAAY,CAAC,QAAQ;wCAC1C,MAAM,YACJ,aAAa,eAAe,KAAK,SAC7B,aAAa,eAAe,EAAE,KAC9B,aAAa,aAAa,EAAE;wCAElC,IAAI,cAAc,uBAAuB;4CACvC,QAAQ,GAAG,CACT,CAAC,0EAA0E,CAAC;4CAE9E;wCACF;wCAEA,IAAI,iBAAiB,OAAO,EAAE;4CAC5B,2DAA2D;4CAC3D,MAAM,kBAAkB,iBAAiB,OAAO,CAAC,YAAY;4CAC7D,MAAM,mBAAmB,kBAAkB;4CAC3C,sEAAsE;4CACtE,iBAAiB,OAAO,CAAC,QAAQ,CAAC;gDAChC,KAAK,iBAAiB;gDACtB,UAAU;4CACZ;4CACA,QAAQ,GAAG,CACT,CAAC,gEAAgE,CAAC;wCAEtE;oCACF;;4BACF;;oBACF;gBACF;;YAEA,cAAc,gBAAgB,CAAC,UAAU;YACzC;sCAAO;oBACL,cAAc,mBAAmB,CAAC,UAAU;gBAC9C;;QACA,uDAAuD;QACzD;6BAAG;QACD;QACA;QACA,iBAAiB;QACjB,eAAe;QACf;KAED;IAED,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,4BAA4B;YAC5B,IAAI,oBAAoB,UAAU,iBAAiB,IAAI;gBACrD,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,gBAAgB,EAAE,EAAE;gBAGjE,iEAAiE;gBACjE,8DAA8D;gBAE9D,4BAA4B;gBAC5B;gBAEA,sDAAsD;gBACtD,MAAM;gEAAwB;wBAC5B,IAAI;4BACF,qEAAqE;4BACrE,MAAM,uBAAuB,cAAc,IAAI;iGAC7C,CAAC,OACC,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,KAAK,gBAAgB,EAAE;;4BAGlE,8EAA8E;4BAC9E,IAAI,sBAAsB,SAAS,UAAU,UAAU;gCACrD,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,gBAAgB,EAAE,CAAC,yBAAyB,CAAC;gCAE1F,MAAM,EAAE,kBAAkB,EAAE,GAAG,6HAAA,CAAA,eAAY,CAAC,QAAQ;gCACpD,mBACE,qBAAqB,OAAO;gCAE9B;4BACF;4BAEA,4BAA4B;4BAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,EAAE;4BACvD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;gCACjC,kEAAkE;gCAClE,MAAM,yBACJ,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,eAAe;gCAEzC,wDAAwD;gCACxD,IAAI,wBAAwB,OAAO,gBAAgB,EAAE,EAAE;oCACrD,sDAAsD;oCACtD,MAAM,EAAE,kBAAkB,EAAE,GAAG,6HAAA,CAAA,eAAY,CAAC,QAAQ;oCACpD,yBAAyB;oCACzB,MAAM,OAAO,OAAO,IAAI;oCACxB,IAAI,CAAC,KAAK,QAAQ,EAAE;wCAClB,KAAK,QAAQ,GAAG;4CACd,IAAI,KAAK,EAAE;4CACX,UAAU,KAAK,KAAK,IAAI,KAAK,WAAW,IAAI;4CAC5C,mBAAmB;4CACnB,eAAe;4CACf,gBAAgB;4CAChB,WAAW,IAAI;4CACf,WAAW,IAAI;4CACf,UAAU;wCACZ;oCACF;oCACA,mBAAmB;gCACrB,OAAO;oCACL,QAAQ,GAAG,CACT,CAAC,wEAAwE,CAAC;gCAE9E;4BACF;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,sCAAsC;wBACtD;oBACF;;gBAEA;YACF,OAEK,IAAI,oBAAoB,WAAW,eAAe,IAAI;gBACzD,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,cAAc,EAAE,EAAE;gBAEvE,iEAAiE;gBACjE,8DAA8D;gBAE9D,4BAA4B;gBAC5B;YAEA,kFAAkF;YAClF,mDAAmD;YACrD;QACA,uDAAuD;QACzD;6BAAG;QACD,iBAAiB;QACjB,eAAe;QACf;KAED;IAED,8CAA8C;IAC9C,sCAAsC;IACtC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAC1D,kFAAkF;IAClF,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAW;IAC/C,uFAAuF;IACvF,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAEtD,2EAA2E;IAC3E,6CAA6C;IAC7C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDACnC,CAAC;YACC,0CAA0C;YAC1C,IAAI,uBAAuB,OAAO,KAAK,WAAW;gBAChD,uBAAuB;gBACvB,uBAAuB,OAAO,GAAG;gBAEjC,wBAAwB;gBACxB,YAAY;gBAEZ,sDAAsD;gBACtD,IAAI,WAAW;oBACb,yEAAyE;oBACzE;oEAAW;4BACT,eAAe;wBACjB;mEAAG;gBACL;YACF;QACF;mDACA;QAAC;KAAe;IAGlB,sEAAsE;IACtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,mCAAmC;YACnC,IAAI,AAAC,CAAC,mBAAmB,CAAC,iBAAkB,CAAC,iBAAiB;gBAC5D,mBAAmB;gBACnB;YACF;YAEA,MAAM,iBACJ,oBAAoB,SAAS,iBAAiB,KAAK,eAAe;YAEpE,IAAI,CAAC,gBAAgB;gBACnB,mBAAmB;gBACnB;YACF;YAEA,yDAAyD;YACzD,IAAI,wBAAwB,OAAO,KAAK,gBAAgB;gBACtD;YACF;YAEA,QAAQ,GAAG,CACT,CAAC,2CAA2C,EAAE,gBAAgB,EAAE,EAAE,gBAAgB;YAGpF,qCAAqC;YACrC,wBAAwB,OAAO,GAAG;YAElC,8CAA8C;YAC9C,IAAI,sBAAsB,OAAO,EAAE;gBACjC,sBAAsB,OAAO;gBAC7B,sBAAsB,OAAO,GAAG;YAClC;YAEA,6EAA6E;YAC7E,IAAI,sBAAsB;YAC1B,IAAI,oBAAoB,QAAQ;gBAC9B,MAAM,eAAe,cAAc,IAAI;uDACrC,CAAC,OACC,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,KAAK,iBAAiB;;gBAEjE,sBAAsB,CAAC,CAAC,cAAc;YACxC,OAAO;gBACL,MAAM,eAAe,cAAc,IAAI;uDACrC,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO,eAAe;;gBAEvE,sBAAsB,CAAC,CAAC,cAAc;YACxC;YAEA,+BAA+B;YAC/B,mBAAmB;YAEnB,4DAA4D;YAC5D,MAAM,aAAa;YACnB,MAAM,WAAW;YAEjB,oFAAoF;YACpF,IAAI,mBAAmB;YAEvB,uBAAuB;YACvB,MAAM,cAAc,sIAAA,CAAA,wBAAqB,CAAC,SAAS;kDAAC,CAAC;oBACnD,oEAAoE;oBACpE,IAAI,wBAAwB,OAAO,KAAK,UAAU;wBAChD;oBACF;oBAEA,qEAAqE;oBACrE,MAAM,eAAe,6HAAA,CAAA,eAAY,CAAC,QAAQ;oBAC1C,MAAM,YACJ,aAAa,eAAe,KAAK,SAC7B,aAAa,eAAe,EAAE,KAC9B,aAAa,aAAa,EAAE;oBAElC,IACE,CAAC,aACD,cAAc,YACd,aAAa,eAAe,KAAK,YACjC;wBACA,0DAA0D;wBAC1D;oBACF;oBAEA,gCAAgC;oBAChC,IAAI;oBACJ,IAAI,eAAe,QAAQ;wBACzB,sBAAsB,MAAM,aAAa,CAAC,IAAI;8DAC5C,CAAC,OAAS,KAAK,IAAI,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,KAAK;;oBAE1D,OAAO;wBACL,sBAAsB,MAAM,aAAa,CAAC,IAAI;8DAC5C,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO;;oBAE1D;oBAEA,4BAA4B;oBAC5B,MAAM,kBAAkB,CAAC,CAAC,qBAAqB;oBAE/C,wCAAwC;oBACxC,IAAI,oBAAoB,kBAAkB;wBACxC,mBAAmB;wBACnB,2EAA2E;wBAC3E;8DAAsB;gCACpB,oEAAoE;gCACpE,IAAI,wBAAwB,OAAO,KAAK,UAAU;oCAChD,mBAAmB;gCACrB;4BACF;;oBACF;gBACF;;YAEA,4CAA4C;YAC5C,sBAAsB,OAAO,GAAG;YAEhC;sCAAO;oBACL,iEAAiE;oBACjE,IAAI,sBAAsB,OAAO,EAAE;wBACjC,sBAAsB,OAAO;wBAC7B,sBAAsB,OAAO,GAAG;oBAClC;oBACA,sBAAsB;oBACtB,mBAAmB;oBACnB,gCAAgC;oBAChC,wBAAwB,OAAO,GAAG;gBACpC;;QACA,uDAAuD;QACzD;6BAAG;QACD,iBAAiB;QACjB,eAAe;QACf;KAGD;IAED,MAAM,cAAc,CAAC;QACnB,cAAc;IAChB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,sCAAsC;QACtC,IACE,QAAQ,OAAO,CAAC,KAAK,EAAE,UACvB,QAAQ,OAAO,CAAC,KAAK,IACrB,QAAQ,OAAO,CAAC,KAAK,EACrB;YACA,mBAAmB;YACnB,gBAAgB;QAClB,OAAO,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;QAC/B,qCAAqC;QACvC;IACF;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC,MAAc;QACvC,YAAY,MAAM,OAAO;IAC3B;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,MAAM,SAAkD,EAAE;QAC1D,IAAI,cAAc;QAClB,IAAI,eAA0B,EAAE;QAEhC,SAAS,OAAO,CAAC,CAAC;YAChB,kCAAkC;YAClC,IAAI,CAAC,QAAQ,SAAS,EAAE;gBACtB,QAAQ,IAAI,CAAC,8BAA8B;gBAC3C;YACF;YAEA,8CAA8C;YAC9C,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,SAAS;YAEvD,IAAI,gBAAgB,aAAa;gBAC/B,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,OAAO,IAAI,CAAC;wBAAE,MAAM;wBAAa,UAAU;oBAAa;gBAC1D;gBACA,cAAc;gBACd,eAAe;oBAAC;iBAAQ;YAC1B,OAAO;gBACL,aAAa,IAAI,CAAC;YACpB;QACF;QAEA,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,OAAO,IAAI,CAAC;gBAAE,MAAM;gBAAa,UAAU;YAAa;QAC1D;QAEA,OAAO;IACT;IAEA,8EAA8E;IAC9E,MAAM,4BAA4B,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,CAAC,WAAW;YACzC,QAAQ,KAAK,CACX,uDACA;YAEF,OAAO,EAAE;QACX;QAEA,0DAA0D;QAC1D,8EAA8E;QAC9E,MAAM,gBAAsD,CAAC;QAE7D,OAAO,SACJ,GAAG,CAAC,CAAC,SAAS,OAAO;YACpB,0BAA0B;YAC1B,IAAI,CAAC,SAAS;gBACZ,QAAQ,KAAK,CAAC;gBACd,OAAO;oBACL,SAAS,CAAC;oBACV,eAAe;oBACf,YAAY;oBACZ,UAAU;gBACZ;YACF;YAEA,sCAAsC;YACtC,MAAM,gBAAgB,aAAa,KAC/B,QAAQ,QAAQ,KAAK,YAAY,EAAE,GACnC;YACJ,MAAM,cAAc,QAAQ,IAAI,KAAK,CAAC,QAAQ,EAAE,GAAG;YACnD,MAAM,aACJ,CAAC,eAAe,YAAY,QAAQ,KAAK,QAAQ,QAAQ;YAE3D,uDAAuD;YACvD,IAAI,iBAAiB,aAAa,UAAU;gBAC1C,OAAO;oBACL;oBACA;oBACA;oBACA,UAAU,YAAY,QAAQ;gBAChC;YACF;YAEA,2DAA2D;YAC3D,IAAI,aAAa,CAAC,QAAQ,QAAQ,CAAC,EAAE;gBACnC,OAAO;oBACL;oBACA;oBACA;oBACA,UAAU,aAAa,CAAC,QAAQ,QAAQ,CAAC;gBAC3C;YACF;YAEA,yCAAyC;YACzC,IAAI,WAAW,QAAQ,MAAM,EAAE;YAE/B,2DAA2D;YAC3D,IAAI,YAAY,SAAS,QAAQ,EAAE;gBACjC,aAAa,CAAC,QAAQ,QAAQ,CAAC,GAAG;gBAClC,OAAO;oBAAE;oBAAS;oBAAe;oBAAY;gBAAS;YACxD;YAEA,qEAAqE;YACrE,IAAI,oBAAoB,WAAW,CAAC,eAAe;gBACjD,2CAA2C;gBAC3C,MAAM,oBAAoB,eAAe,KACvC,CAAC,OACC,MAAM,SAAS,WACf,CAAC,MAAM,OAAO,OAAO,QAAQ,OAAO,IACjC,iBAAiB,MAAM,OAAO,OAAO,cAAc,EAAE,AAAC;gBAW7D,IAAI,aAAgC;gBAEpC,sCAAsC;gBACtC,IAAI,mBAAmB,OAAO,aAAa;oBACzC,MAAM,aAAa,kBAAkB,KAAK,CAAC,WAAW,CAAC,IAAI,CACzD,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,QAAQ;oBAGlC,IAAI,YAAY;wBACd,aAAa;oBACf;gBACF;gBAEA,yDAAyD;gBACzD,IAAI,CAAC,cAAc,mBAAmB,OAAO;oBAS3C,iDAAiD;oBACjD,MAAM,aAAa,AACjB,kBAAkB,KAAK,CAGvB,OAAO;oBAET,IAAI,cAAc,MAAM,OAAO,CAAC,aAAa;wBAC3C,MAAM,SAAS,WAAW,IAAI,CAC5B,CAAC,IAAsB,EAAE,MAAM,KAAK,QAAQ,QAAQ;wBAGtD,IAAI,QAAQ;4BACV,aAAa;gCACX,IAAI,OAAO,MAAM;gCACjB,UAAU,OAAO,QAAQ;gCACzB,mBAAmB,OAAO,iBAAiB;gCAC3C,MAAM,OAAO,IAAI;4BACnB;wBACF;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,IAAI,CAAC,cAAc,eAAe;oBAChC,mCAAmC;oBACnC,IAAI,cAAc,WAAW,EAAE;wBAC7B,MAAM,aAAa,cAAc,WAAW,CAAC,IAAI,CAC/C,CAAC,IAAM,EAAE,EAAE,KAAK,QAAQ,QAAQ;wBAGlC,IAAI,YAAY;4BACd,aAAa;wBACf;oBACF;oBAEA,sDAAsD;oBACtD,IAAI,CAAC,cAAc,cAAc,OAAO,EAAE;wBACxC,MAAM,SAAS,cAAc,OAAO,CAAC,IAAI,CACvC,CAAC,IAAmB,EAAE,MAAM,KAAK,QAAQ,QAAQ;wBAGnD,IAAI,QAAQ;4BACV,0CAA0C;4BAC1C,aAAa;gCACX,IAAI,OAAO,MAAM;gCACjB,UAAU,OAAO,IAAI,EAAE,UAAU,YAAY;gCAC7C,mBAAmB,OAAO,IAAI,EAAE,UAAU;gCAC1C,MAAM,OAAO,IAAI;4BACnB;wBACF;oBACF;gBACF;gBAEA,kDAAkD;gBAClD,IAAI,YAAY;oBACd,WAAW;wBACT,IAAI,WAAW,EAAE;wBACjB,UAAU,WAAW,QAAQ,IAAI;wBACjC,mBAAmB,WAAW,iBAAiB;wBAC/C,WAAW,IAAI;wBACf,WAAW,IAAI;wBACf,gBAAgB;wBAChB,UAAU;4BAAE,IAAI,WAAW,EAAE;wBAAC;oBAChC;oBAEA,+BAA+B;oBAC/B,aAAa,CAAC,QAAQ,QAAQ,CAAC,GAAG;oBAElC,qEAAqE;oBACrE,IAAI,QAAQ,MAAM,EAAE;wBAClB,QAAQ,MAAM,CAAC,QAAQ,GAAG;oBAC5B,OAAO;wBACL,QAAQ,MAAM,GAAG;4BACf,IAAI,WAAW,EAAE;4BACjB,UAAU;wBACZ;oBACF;gBACF;YACF;YAEA,wDAAwD;YACxD,IACE,CAAC,YACD,oBAAoB,UACpB,iBAAiB,UACjB;gBACA,WAAW,gBAAgB,QAAQ;gBACnC,aAAa,CAAC,QAAQ,QAAQ,CAAC,GAAG;YACpC;YAEA,wEAAwE;YACxE,IAAI,aAAa,MAAM;gBACrB,WAAW;YACb;YAEA,OAAO;gBAAE;gBAAS;gBAAe;gBAAY;YAAS;QACxD,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,OAAO,IAAI,KAAK,OAAO,CAAC,EAAE,GAAG,8BAA8B;IACtF;IAEA,MAAM,gBAAgB;IAEtB,iDAAiD;IACjD,MAAM,uBAAuB;QAC3B,6CAA6C;QAC7C,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG;YAC1C,qBACE;;kCACE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAwB;4CAClB,cAAc,MAAM;4CAAC;;;;;;;kDAE1C,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;;0DAET,6LAAC,+LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;4BAKjC,0BAA0B,eAAe,GAAG,CAC3C,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,iBAC/C,6LAAC,4IAAA,CAAA,UAAW;oCAEV,SAAS;oCACT,eAAe;oCACf,YAAY;oCACZ,SAAS;oCACT,gBAAgB;oCAChB,WAAW;oCACX,UAAU;oCACV,gBAAgB,oBAAoB;mCAR/B,QAAQ,EAAE;;;;;;;;;;;oBAetB,0BACC,6LAAC;wBAAI,WAAU;kCACZ,oBAAoB,UAAU,gCAC7B,6LAAC,gJAAA,CAAA,UAAe;4BACd,SAAS;4BACT,UAAU;;;;;mCAEV,oBAAoB,WAAW,8BACjC,6LAAC,gJAAA,CAAA,UAAe;4BACd,OAAO;4BACP,UAAU;4BACV,aACE,cAAc,IAAI,CAChB,CAAC,IACC,EAAE,IAAI,KAAK,WAAW,EAAE,KAAK,EAAE,OAAO,cAAc,EAAE,GACvD;;;;;mCAGL;;;;;;;;QAKd;QAEA,iDAAiD;QACjD,qBACE;;gBACG,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAC,OAAO,2BACxB,6LAAC;wBAAqB,WAAU;;0CAC9B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI;;;;;;;;;;;4BAId,0BAA0B,MAAM,QAAQ,EAAE,GAAG,CAC5C,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,iBAC/C,6LAAC,4IAAA,CAAA,UAAW;oCAEV,SAAS;oCACT,eAAe;oCACf,YAAY;oCACZ,SAAS;oCACT,gBAAgB;oCAChB,UAAU;oCACV,gBAAgB,oBAAoB;mCAP/B,QAAQ,EAAE;;;;;;uBAVb;;;;8CAwBZ,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;gBAOhC,0BACC,6LAAC;oBAAI,WAAU;8BACZ,oBAAoB,UAAU,gCAC7B,6LAAC,gJAAA,CAAA,UAAe;wBAAC,SAAS;wBAAiB,UAAU;;;;;+BACnD,oBAAoB,WAAW,8BACjC,6LAAC,gJAAA,CAAA,UAAe;wBACd,OAAO;wBACP,UAAU;wBACV,aACE,cAAc,IAAI,CAChB,CAAC,IACC,EAAE,IAAI,KAAK,WAAW,EAAE,KAAK,EAAE,OAAO,cAAc,EAAE,GACvD;;;;;+BAGL;;;;;;;;IAKd;IAEA,4GAA4G;IAC5G,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,4BAA4B;YAC5B,IAAI,oBAAoB,WAAW,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE;gBACtE;YACF;YAEA,qDAAqD;YACrD,MAAM,iBAAiB,cAAc,EAAE;YAEvC,wEAAwE;YACxE,MAAM,sBAAsB;0DAAW;oBACrC,qCAAqC;oBACrC,MAAM,uBACJ,sIAAA,CAAA,wBAAqB,CAAC,QAAQ,GAAG,aAAa;oBAEhD,kCAAkC;oBAClC,MAAM,cAAc,qBAAqB,IAAI;8EAC3C,CAAC,OAAS,KAAK,IAAI,KAAK,WAAW,KAAK,KAAK,EAAE,OAAO;;oBAGxD,yCAAyC;oBACzC,IAAI,CAAC,aAAa;wBAChB,QAAQ,GAAG,CACT,CAAC,iBAAiB,EAAE,eAAe,gDAAgD,CAAC;wBAEtF,6EAA6E;wBAC7E,MAAM,mBAAmB,6HAAA,CAAA,eAAY,CAAC,QAAQ;wBAC9C,IAAI,iBAAiB,aAAa,EAAE,OAAO,gBAAgB;4BACzD,6HAAA,CAAA,eAAY,CAAC,QAAQ,GAAG,gBAAgB,CAAC;wBAC3C;oBACF;gBACF;yDAAG,MAAM,uCAAuC;YAEhD,uBAAuB;YACvB;sCAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;6BAAG;QACD;QACA,eAAe;KAEhB;IAED,qFAAqF;IACrF,MAAM,cAAc,CAAC,CAAC,aAAa;IAEnC,2DAA2D;IAC3D,IAAI,CAAC,aAAa;QAChB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,oBAAoB,uBACnB,6LAAC,2IAAA,CAAA,UAAU;gBACT,SAAS;gBACT,cAAc;gBACd,cAAc;;;;;qCAGhB,6LAAC,gJAAA,CAAA,UAAe;gBACd,OAAO;gBACP,cAAc;gBACd,cAAc;;;;;;0BAIlB,6LAAC,qJAAA,CAAA,UAAoB;gBACnB,YAAY,CAAC,QAAU,kBAAkB,IAAI;0BAE7C,cAAA,6LAAC;oBACC,KAAK;oBACL,WAAU;;wBAET,mBAAmB,gBAClB,0BACE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;iDAIjC;;gCACG,gCACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;4CAAgI;;;;;;;;;;;;gCAKpJ;;yDAIL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;sCAKjC,6LAAC;4BAAI,KAAK;;;;;;;;;;;;;;;;;0BAId,6LAAC,6IAAA,CAAA,UAAY;gBACX,eAAe;gBACf,UAAU,AAAC,CAAC,mBAAmB,CAAC,iBAAkB;gBAClD,YAAY;gBACZ,eAAe;;;;;;0BAIjB,6LAAC,oJAAA,CAAA,UAAmB;gBAClB,SAAS;gBACT,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,UACE,iBAAiB,QAAQ,YACzB,CAAC,oBAAoB,SAAS,iBAAiB,WAAW,SAAS;;;;;;;;;;;;AAK7E;GAljCwB;;QAyBlB,6HAAA,CAAA,eAAY;QAE6B,sIAAA,CAAA,wBAAqB;QAG1C,qIAAA,CAAA,uBAAoB;;;KA9BtB", "debugId": null}}, {"offset": {"line": 6441, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/MediaGalleryView.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { ArrowLeft, Video } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Media } from \"@/types/base\";\r\nimport Image from \"next/image\";\r\nimport { getLinkIcon, getLinkTitle } from \"@/utils/link-utils\";\r\nimport MediaViewer from \"@/components/media/MediaViewer\";\r\n\r\ninterface MediaGalleryViewProps {\r\n  mediaFiles: (Media & { createdAt: Date })[];\r\n  documents?: (Media & { createdAt: Date })[];\r\n  links?: { url: string; title: string; timestamp: Date }[];\r\n  initialTab?: TabType;\r\n  onClose: () => void;\r\n}\r\n\r\ntype SortOption = \"date\" | \"sender\";\r\ntype TabType = \"media\" | \"files\" | \"links\";\r\n\r\ninterface MediaByDate {\r\n  date: string;\r\n  media: (Media & { createdAt: Date })[];\r\n}\r\n\r\nexport default function MediaGalleryView({\r\n  mediaFiles,\r\n  documents = [],\r\n  links = [],\r\n  initialTab = \"media\",\r\n  onClose,\r\n}: MediaGalleryViewProps) {\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  const [sortBy, setSortBy] = useState<SortOption>(\"date\");\r\n  const [mediaByDate, setMediaByDate] = useState<MediaByDate[]>([]);\r\n  const [activeTab, setActiveTab] = useState<TabType>(initialTab);\r\n  const [showMediaViewer, setShowMediaViewer] = useState(false);\r\n  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);\r\n  const [documentsByDate, setDocumentsByDate] = useState<MediaByDate[]>([]);\r\n  const [linksByDate, setLinksByDate] = useState<\r\n    { date: string; links: { url: string; title: string; timestamp: Date }[] }[]\r\n  >([]);\r\n\r\n  // Search states\r\n  const [fileSearchTerm, setFileSearchTerm] = useState(\"\");\r\n  const [linkSearchTerm, setLinkSearchTerm] = useState(\"\");\r\n  const [filteredDocumentsByDate, setFilteredDocumentsByDate] = useState<\r\n    MediaByDate[]\r\n  >([]);\r\n  const [filteredLinksByDate, setFilteredLinksByDate] = useState<\r\n    { date: string; links: { url: string; title: string; timestamp: Date }[] }[]\r\n  >([]);\r\n\r\n  // Format date for header (e.g., \"Ngày 16 Tháng 4\")\r\n  const formatDateHeader = (date: Date): string => {\r\n    const day = date.getDate();\r\n    const month = date.getMonth() + 1;\r\n    return `Ngày ${day} Tháng ${month}`;\r\n  };\r\n\r\n  // Helper function to determine if a media is a video\r\n  const isVideo = (media: Media): boolean => {\r\n    // Check if type is explicitly VIDEO\r\n    if (media.type === \"VIDEO\") {\r\n      return true;\r\n    }\r\n\r\n    // Check if extension is a video extension and type is not explicitly IMAGE\r\n    if (\r\n      media.metadata?.extension?.match(/mp4|webm|mov/i) &&\r\n      media.type !== \"IMAGE\"\r\n    ) {\r\n      return true;\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  // Helper function to ensure media type is correctly set\r\n  const ensureCorrectMediaType = (\r\n    media: Media & { createdAt: Date },\r\n  ): Media & { createdAt: Date } => {\r\n    // Create a new object with the correct type\r\n    return {\r\n      ...media,\r\n      // Ensure type is set correctly for videos\r\n      type: isVideo(media) ? \"VIDEO\" : \"IMAGE\",\r\n    };\r\n  };\r\n\r\n  // Group media by date\r\n  useEffect(() => {\r\n    const groupMediaByDate = () => {\r\n      const groups: { [key: string]: (Media & { createdAt: Date })[] } = {};\r\n\r\n      // Sort media files by date (newest first)\r\n      const sortedMedia = [...mediaFiles].sort(\r\n        (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n      );\r\n\r\n      // Log media files for debugging\r\n      console.log(\r\n        \"Media files:\",\r\n        sortedMedia.map((m) => ({\r\n          fileId: m.fileId,\r\n          fileName: m.fileName,\r\n          type: m.type,\r\n          extension: m.metadata?.extension,\r\n        })),\r\n      );\r\n\r\n      sortedMedia.forEach((media) => {\r\n        const date = formatDateHeader(media.createdAt);\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n        groups[date].push(media);\r\n      });\r\n\r\n      // Convert to array format for rendering\r\n      const result: MediaByDate[] = Object.keys(groups).map((date) => ({\r\n        date,\r\n        media: groups[date],\r\n      }));\r\n\r\n      setMediaByDate(result);\r\n    };\r\n\r\n    groupMediaByDate();\r\n  }, [mediaFiles]);\r\n\r\n  // Group documents by date\r\n  useEffect(() => {\r\n    const groupDocumentsByDate = () => {\r\n      const groups: { [key: string]: (Media & { createdAt: Date })[] } = {};\r\n\r\n      // Sort documents by date (newest first)\r\n      const sortedDocuments = [...documents].sort(\r\n        (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n      );\r\n\r\n      sortedDocuments.forEach((doc) => {\r\n        const date = formatDateHeader(doc.createdAt);\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n        groups[date].push(doc);\r\n      });\r\n\r\n      // Convert to array format for rendering\r\n      const result: MediaByDate[] = Object.keys(groups).map((date) => ({\r\n        date,\r\n        media: groups[date],\r\n      }));\r\n\r\n      setDocumentsByDate(result);\r\n      setFilteredDocumentsByDate(result); // Initialize filtered documents with all documents\r\n    };\r\n\r\n    groupDocumentsByDate();\r\n  }, [documents]);\r\n\r\n  // Group links by date\r\n  useEffect(() => {\r\n    const groupLinksByDate = () => {\r\n      const groups: {\r\n        [key: string]: { url: string; title: string; timestamp: Date }[];\r\n      } = {};\r\n\r\n      // Sort links by date (newest first)\r\n      const sortedLinks = [...links].sort(\r\n        (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),\r\n      );\r\n\r\n      sortedLinks.forEach((link) => {\r\n        const date = formatDateHeader(link.timestamp);\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n        groups[date].push(link);\r\n      });\r\n\r\n      // Convert to array format for rendering\r\n      const result = Object.keys(groups).map((date) => ({\r\n        date,\r\n        links: groups[date],\r\n      }));\r\n\r\n      setLinksByDate(result);\r\n      setFilteredLinksByDate(result); // Initialize filtered links with all links\r\n    };\r\n\r\n    groupLinksByDate();\r\n  }, [links]);\r\n\r\n  return (\r\n    <div className=\"flex flex-col bg-white h-full w-full\">\r\n      {/* Header */}\r\n      <div className=\"p-4 flex  items-center justify-between border-b\">\r\n        <div className=\"flex items-center\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"mr-2\"\r\n            onClick={onClose}\r\n          >\r\n            <ArrowLeft className=\"h-5 w-5\" />\r\n          </Button>\r\n          <h2 className=\"font-semibold\">Kho lưu trữ</h2>\r\n        </div>\r\n        <Button variant=\"ghost\" size=\"sm\" className=\"text-sm\">\r\n          Chọn\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Tab buttons */}\r\n      <div className=\"w-full grid grid-cols-3 border-b rounded-none h-12 flex-shrink-0\">\r\n        <button\r\n          onClick={() => setActiveTab(\"media\")}\r\n          className={`text-sm ${activeTab === \"media\" ? \"border-b-2 border-blue-500 font-medium\" : \"\"}`}\r\n        >\r\n          Ảnh/Video\r\n        </button>\r\n        <button\r\n          onClick={() => setActiveTab(\"files\")}\r\n          className={`text-sm ${activeTab === \"files\" ? \"border-b-2 border-blue-500 font-medium\" : \"\"}`}\r\n        >\r\n          Files\r\n        </button>\r\n        <button\r\n          onClick={() => setActiveTab(\"links\")}\r\n          className={`text-sm ${activeTab === \"links\" ? \"border-b-2 border-blue-500 font-medium\" : \"\"}`}\r\n        >\r\n          Links\r\n        </button>\r\n      </div>\r\n\r\n      {/* Content area */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Media content */}\r\n        {activeTab === \"media\" && (\r\n          <div className=\"overflow-hidden flex flex-col flex-1\">\r\n            {/* Media content */}\r\n            <div className=\"flex-1 overflow-y-auto w-full h-full\">\r\n              {mediaByDate.length > 0 ? (\r\n                mediaByDate.map((group) => (\r\n                  <div key={group.date} className=\"w-full mb-4\">\r\n                    <h3 className=\"font-medium text-sm px-3 py-2 sticky top-0 bg-white z-10\">\r\n                      {group.date}\r\n                    </h3>\r\n                    <div className=\"grid grid-cols-3 w-full gap-1 px-1\">\r\n                      {group.media.map((media, index) => (\r\n                        <div\r\n                          key={`${media.fileId}-${index}`}\r\n                          className=\"aspect-square rounded-sm overflow-hidden cursor-pointer\"\r\n                          onClick={() => {\r\n                            // Find the index of this media in the full mediaFiles array\r\n                            const fullIndex = mediaFiles.findIndex(\r\n                              (m) => m.fileId === media.fileId,\r\n                            );\r\n                            const selectedIndex =\r\n                              fullIndex >= 0 ? fullIndex : index;\r\n                            console.log(\r\n                              \"Opening MediaViewer with index:\",\r\n                              selectedIndex,\r\n                            );\r\n                            console.log(\"Selected media:\", media);\r\n                            console.log(\"Is video:\", isVideo(media));\r\n                            setSelectedMediaIndex(selectedIndex);\r\n                            setShowMediaViewer(true);\r\n                          }}\r\n                        >\r\n                          {isVideo(media) ? (\r\n                            <div className=\"w-full h-full bg-black relative\">\r\n                              <video\r\n                                key={`gallery-video-${media.fileId}`}\r\n                                src={media.url}\r\n                                className=\"object-cover w-full h-full\"\r\n                                preload=\"metadata\"\r\n                                muted\r\n                                onError={(e) => {\r\n                                  console.error(\r\n                                    \"Gallery video error:\",\r\n                                    e,\r\n                                    media,\r\n                                  );\r\n                                }}\r\n                              />\r\n                              <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                                <Video className=\"h-6 w-6 text-white\" />\r\n                              </div>\r\n                            </div>\r\n                          ) : (\r\n                            <Image\r\n                              src={media.url}\r\n                              alt={media.fileName}\r\n                              className=\"object-cover w-full h-full\"\r\n                              width={500}\r\n                              height={500}\r\n                              unoptimized\r\n                            />\r\n                          )}\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                    <div className=\"text-xs text-gray-500 px-3 py-1\">\r\n                      {group.media.length}{\" \"}\r\n                      {group.media.some(isVideo) ? \"ảnh/video\" : \"ảnh\"} trong{\" \"}\r\n                      {new Date().getFullYear()}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              ) : (\r\n                <div className=\"flex-1 flex items-center justify-center h-full\">\r\n                  <p className=\"text-gray-500\">\r\n                    Không có hình ảnh hoặc video nào\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Files content */}\r\n        {activeTab === \"files\" && (\r\n          <div className=\"flex-1 overflow-hidden flex flex-col h-full\">\r\n            {/* Search bar */}\r\n            <div className=\"p-2 border-b\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Tìm kiếm file\"\r\n                  className=\"w-full pl-8 pr-4 py-2 rounded-full bg-gray-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n                  value={fileSearchTerm}\r\n                  onChange={(e) => {\r\n                    setFileSearchTerm(e.target.value);\r\n                    // Filter documents based on search term\r\n                    if (e.target.value.trim() === \"\") {\r\n                      setFilteredDocumentsByDate(documentsByDate);\r\n                    } else {\r\n                      const searchTerm = e.target.value.toLowerCase();\r\n                      const filtered = documentsByDate\r\n                        .map((group) => ({\r\n                          date: group.date,\r\n                          media: group.media.filter((doc) =>\r\n                            doc.fileName.toLowerCase().includes(searchTerm),\r\n                          ),\r\n                        }))\r\n                        .filter((group) => group.media.length > 0);\r\n                      setFilteredDocumentsByDate(filtered);\r\n                    }\r\n                  }}\r\n                />\r\n                <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      d=\"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z\"\r\n                      stroke=\"#6B7280\"\r\n                      strokeWidth=\"2\"\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Files list */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n              {filteredDocumentsByDate.length > 0 ? (\r\n                filteredDocumentsByDate.map((group) => (\r\n                  <div key={group.date} className=\"mb-4\">\r\n                    <h3 className=\"font-medium text-sm px-3 py-2 sticky top-0 bg-white z-10\">\r\n                      {group.date}\r\n                    </h3>\r\n                    <div className=\"space-y-2\">\r\n                      {group.media.map((doc, index) => (\r\n                        <div\r\n                          key={`${doc.fileId}-${index}`}\r\n                          className=\"flex items-center p-3 hover:bg-gray-100 cursor-pointer\"\r\n                          onClick={() => window.open(doc.url, \"_blank\")}\r\n                        >\r\n                          <div className=\"bg-blue-100 p-2 rounded-md mr-3\">\r\n                            <svg\r\n                              width=\"24\"\r\n                              height=\"24\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              fill=\"none\"\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                            >\r\n                              <path\r\n                                d=\"M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z\"\r\n                                stroke=\"#3B82F6\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M14 2V8H20\"\r\n                                stroke=\"#3B82F6\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M16 13H8\"\r\n                                stroke=\"#3B82F6\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M16 17H8\"\r\n                                stroke=\"#3B82F6\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M10 9H9H8\"\r\n                                stroke=\"#3B82F6\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                            </svg>\r\n                          </div>\r\n                          <div className=\"flex-1 min-w-0\">\r\n                            <p className=\"font-medium text-sm truncate\">\r\n                              {doc.fileName}\r\n                            </p>\r\n                            <p className=\"text-xs text-gray-500\">\r\n                              {doc.metadata?.sizeFormatted ||\r\n                                `${Math.round((doc.metadata?.size || 0) / 1024)} KB`}\r\n                            </p>\r\n                          </div>\r\n                          <button className=\"p-2 text-gray-500 hover:text-gray-700\">\r\n                            <svg\r\n                              width=\"20\"\r\n                              height=\"20\"\r\n                              viewBox=\"0 0 24 24\"\r\n                              fill=\"none\"\r\n                              xmlns=\"http://www.w3.org/2000/svg\"\r\n                            >\r\n                              <path\r\n                                d=\"M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15\"\r\n                                stroke=\"currentColor\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M7 10L12 15L17 10\"\r\n                                stroke=\"currentColor\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                              <path\r\n                                d=\"M12 15V3\"\r\n                                stroke=\"currentColor\"\r\n                                strokeWidth=\"2\"\r\n                                strokeLinecap=\"round\"\r\n                                strokeLinejoin=\"round\"\r\n                              />\r\n                            </svg>\r\n                          </button>\r\n                        </div>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              ) : (\r\n                <div className=\"flex-1 flex flex-col items-center justify-center h-full\">\r\n                  <div className=\"w-16 h-20 bg-blue-50 rounded-lg flex items-center justify-center mb-2\">\r\n                    <div className=\"w-10 h-12 bg-blue-100 rounded-sm relative\">\r\n                      <div className=\"absolute top-0 right-0 w-3 h-3 bg-blue-50 rounded-br-sm\"></div>\r\n                    </div>\r\n                  </div>\r\n                  <p className=\"text-gray-500 text-sm\">Chưa có File</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Links content */}\r\n        {activeTab === \"links\" && (\r\n          <div className=\"flex-1 overflow-hidden flex flex-col h-full\">\r\n            {/* Search bar */}\r\n            <div className=\"p-2 border-b\">\r\n              <div className=\"relative\">\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Tìm kiếm link\"\r\n                  className=\"w-full pl-8 pr-4 py-2 rounded-full bg-gray-100 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500\"\r\n                  value={linkSearchTerm}\r\n                  onChange={(e) => {\r\n                    setLinkSearchTerm(e.target.value);\r\n                    // Filter links based on search term\r\n                    if (e.target.value.trim() === \"\") {\r\n                      setFilteredLinksByDate(linksByDate);\r\n                    } else {\r\n                      const searchTerm = e.target.value.toLowerCase();\r\n                      const filtered = linksByDate\r\n                        .map((group) => ({\r\n                          date: group.date,\r\n                          links: group.links.filter(\r\n                            (link) =>\r\n                              link.title.toLowerCase().includes(searchTerm) ||\r\n                              link.url.toLowerCase().includes(searchTerm),\r\n                          ),\r\n                        }))\r\n                        .filter((group) => group.links.length > 0);\r\n                      setFilteredLinksByDate(filtered);\r\n                    }\r\n                  }}\r\n                />\r\n                <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2\">\r\n                  <svg\r\n                    width=\"16\"\r\n                    height=\"16\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                  >\r\n                    <path\r\n                      d=\"M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z\"\r\n                      stroke=\"#6B7280\"\r\n                      strokeWidth=\"2\"\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Links list */}\r\n            <div className=\"flex-1 overflow-y-auto\">\r\n              {filteredLinksByDate.length > 0 ? (\r\n                filteredLinksByDate.map((group) => (\r\n                  <div key={group.date} className=\"mb-4\">\r\n                    <h3 className=\"font-medium text-sm px-3 py-2 sticky top-0 bg-white z-10\">\r\n                      {group.date}\r\n                    </h3>\r\n                    <div className=\"space-y-2\">\r\n                      {group.links.map((link, index) => {\r\n                        // Extract domain from URL\r\n                        const domain = link.url\r\n                          .replace(/^https?:\\/\\//, \"\")\r\n                          .split(\"/\")[0];\r\n\r\n                        // Format date as DD/MM\r\n                        const date = new Date(link.timestamp);\r\n                        const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/${(date.getMonth() + 1).toString().padStart(2, \"0\")}`;\r\n\r\n                        return (\r\n                          <div\r\n                            key={index}\r\n                            className=\"flex items-center py-2 px-4 hover:bg-gray-100 cursor-pointer\"\r\n                            onClick={() =>\r\n                              window.open(\r\n                                link.url,\r\n                                \"_blank\",\r\n                                \"noopener,noreferrer\",\r\n                              )\r\n                            }\r\n                          >\r\n                            <div className=\"w-10 h-10 rounded-md mr-3 flex items-center justify-center overflow-hidden\">\r\n                              {getLinkIcon(domain)}\r\n                            </div>\r\n                            <div className=\"flex-1 min-w-0 mr-2\">\r\n                              <p className=\"font-medium text-sm truncate max-w-[180px]\">\r\n                                {getLinkTitle(\r\n                                  domain,\r\n                                  link.title.length > 40\r\n                                    ? link.title.substring(0, 40) + \"...\"\r\n                                    : link.title,\r\n                                )}\r\n                              </p>\r\n                              <p className=\"text-xs text-blue-500 truncate\">\r\n                                {domain}\r\n                              </p>\r\n                            </div>\r\n                            <div className=\"text-xs text-gray-500 whitespace-nowrap\">\r\n                              {formattedDate}\r\n                            </div>\r\n                          </div>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </div>\r\n                ))\r\n              ) : (\r\n                <div className=\"flex-1 flex flex-col items-center justify-center h-full\">\r\n                  <div className=\"w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-2\">\r\n                    <svg\r\n                      width=\"32\"\r\n                      height=\"32\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        d=\"M9 17H7C5.89543 17 5 16.1046 5 15V9C5 7.89543 5.89543 7 7 7H9M15 17H17C18.1046 17 19 16.1046 19 15V9C19 7.89543 18.1046 7 17 7H15M9 12H15\"\r\n                        stroke=\"#93C5FD\"\r\n                        strokeWidth=\"2\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                  <p className=\"text-gray-500 text-sm\">Chưa có Link</p>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      {/* Media Viewer */}\r\n      {showMediaViewer && mediaFiles.length > 0 && (\r\n        <MediaViewer\r\n          isOpen={showMediaViewer}\r\n          onClose={() => {\r\n            setShowMediaViewer(false);\r\n          }}\r\n          media={mediaFiles.map(ensureCorrectMediaType)}\r\n          initialIndex={selectedMediaIndex}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAEA;AACA;AACA;;;AARA;;;;;;;AA0Be,SAAS,iBAAiB,EACvC,UAAU,EACV,YAAY,EAAE,EACd,QAAQ,EAAE,EACV,aAAa,OAAO,EACpB,OAAO,EACe;;IACtB,6DAA6D;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE3C,EAAE;IAEJ,gBAAgB;IAChB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEnE,EAAE;IACJ,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE3D,EAAE;IAEJ,mDAAmD;IACnD,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,KAAK,OAAO;QACxB,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,OAAO,CAAC,KAAK,EAAE,IAAI,OAAO,EAAE,OAAO;IACrC;IAEA,qDAAqD;IACrD,MAAM,UAAU,CAAC;QACf,oCAAoC;QACpC,IAAI,MAAM,IAAI,KAAK,SAAS;YAC1B,OAAO;QACT;QAEA,2EAA2E;QAC3E,IACE,MAAM,QAAQ,EAAE,WAAW,MAAM,oBACjC,MAAM,IAAI,KAAK,SACf;YACA,OAAO;QACT;QAEA,OAAO;IACT;IAEA,wDAAwD;IACxD,MAAM,yBAAyB,CAC7B;QAEA,4CAA4C;QAC5C,OAAO;YACL,GAAG,KAAK;YACR,0CAA0C;YAC1C,MAAM,QAAQ,SAAS,UAAU;QACnC;IACF;IAEA,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,MAAM,SAA6D,CAAC;oBAEpE,0CAA0C;oBAC1C,MAAM,cAAc;2BAAI;qBAAW,CAAC,IAAI;mFACtC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;oBAGvD,gCAAgC;oBAChC,QAAQ,GAAG,CACT,gBACA,YAAY,GAAG;uEAAC,CAAC,IAAM,CAAC;gCACtB,QAAQ,EAAE,MAAM;gCAChB,UAAU,EAAE,QAAQ;gCACpB,MAAM,EAAE,IAAI;gCACZ,WAAW,EAAE,QAAQ,EAAE;4BACzB,CAAC;;oBAGH,YAAY,OAAO;uEAAC,CAAC;4BACnB,MAAM,OAAO,iBAAiB,MAAM,SAAS;4BAC7C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gCACjB,MAAM,CAAC,KAAK,GAAG,EAAE;4BACnB;4BACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;wBACpB;;oBAEA,wCAAwC;oBACxC,MAAM,SAAwB,OAAO,IAAI,CAAC,QAAQ,GAAG;8EAAC,CAAC,OAAS,CAAC;gCAC/D;gCACA,OAAO,MAAM,CAAC,KAAK;4BACrB,CAAC;;oBAED,eAAe;gBACjB;;YAEA;QACF;qCAAG;QAAC;KAAW;IAEf,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;mEAAuB;oBAC3B,MAAM,SAA6D,CAAC;oBAEpE,wCAAwC;oBACxC,MAAM,kBAAkB;2BAAI;qBAAU,CAAC,IAAI;2FACzC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;oBAGvD,gBAAgB,OAAO;2EAAC,CAAC;4BACvB,MAAM,OAAO,iBAAiB,IAAI,SAAS;4BAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gCACjB,MAAM,CAAC,KAAK,GAAG,EAAE;4BACnB;4BACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;wBACpB;;oBAEA,wCAAwC;oBACxC,MAAM,SAAwB,OAAO,IAAI,CAAC,QAAQ,GAAG;kFAAC,CAAC,OAAS,CAAC;gCAC/D;gCACA,OAAO,MAAM,CAAC,KAAK;4BACrB,CAAC;;oBAED,mBAAmB;oBACnB,2BAA2B,SAAS,mDAAmD;gBACzF;;YAEA;QACF;qCAAG;QAAC;KAAU;IAEd,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,MAAM,SAEF,CAAC;oBAEL,oCAAoC;oBACpC,MAAM,cAAc;2BAAI;qBAAM,CAAC,IAAI;mFACjC,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;oBAGvD,YAAY,OAAO;uEAAC,CAAC;4BACnB,MAAM,OAAO,iBAAiB,KAAK,SAAS;4BAC5C,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gCACjB,MAAM,CAAC,KAAK,GAAG,EAAE;4BACnB;4BACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;wBACpB;;oBAEA,wCAAwC;oBACxC,MAAM,SAAS,OAAO,IAAI,CAAC,QAAQ,GAAG;8EAAC,CAAC,OAAS,CAAC;gCAChD;gCACA,OAAO,MAAM,CAAC,KAAK;4BACrB,CAAC;;oBAED,eAAe;oBACf,uBAAuB,SAAS,2CAA2C;gBAC7E;;YAEA;QACF;qCAAG;QAAC;KAAM;IAEV,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;gCAAG,WAAU;0CAAgB;;;;;;;;;;;;kCAEhC,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,WAAU;kCAAU;;;;;;;;;;;;0BAMxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,QAAQ,EAAE,cAAc,UAAU,2CAA2C,IAAI;kCAC9F;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,QAAQ,EAAE,cAAc,UAAU,2CAA2C,IAAI;kCAC9F;;;;;;kCAGD,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,QAAQ,EAAE,cAAc,UAAU,2CAA2C,IAAI;kCAC9F;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;oBAEZ,cAAc,yBACb,6LAAC;wBAAI,WAAU;kCAEb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,YAAY,MAAM,GAAG,IACpB,YAAY,GAAG,CAAC,CAAC,sBACf,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CAAG,WAAU;sDACX,MAAM,IAAI;;;;;;sDAEb,6LAAC;4CAAI,WAAU;sDACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,sBACvB,6LAAC;oDAEC,WAAU;oDACV,SAAS;wDACP,4DAA4D;wDAC5D,MAAM,YAAY,WAAW,SAAS,CACpC,CAAC,IAAM,EAAE,MAAM,KAAK,MAAM,MAAM;wDAElC,MAAM,gBACJ,aAAa,IAAI,YAAY;wDAC/B,QAAQ,GAAG,CACT,mCACA;wDAEF,QAAQ,GAAG,CAAC,mBAAmB;wDAC/B,QAAQ,GAAG,CAAC,aAAa,QAAQ;wDACjC,sBAAsB;wDACtB,mBAAmB;oDACrB;8DAEC,QAAQ,uBACP,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAEC,KAAK,MAAM,GAAG;gEACd,WAAU;gEACV,SAAQ;gEACR,KAAK;gEACL,SAAS,CAAC;oEACR,QAAQ,KAAK,CACX,wBACA,GACA;gEAEJ;+DAXK,CAAC,cAAc,EAAE,MAAM,MAAM,EAAE;;;;;0EAatC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;;;;;;6EAIrB,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK,MAAM,GAAG;wDACd,KAAK,MAAM,QAAQ;wDACnB,WAAU;wDACV,OAAO;wDACP,QAAQ;wDACR,WAAW;;;;;;mDA9CV,GAAG,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;sDAoDrC,6LAAC;4CAAI,WAAU;;gDACZ,MAAM,KAAK,CAAC,MAAM;gDAAE;gDACpB,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,cAAc;gDAAM;gDAAO;gDACvD,IAAI,OAAO,WAAW;;;;;;;;mCA9DjB,MAAM,IAAI;;;;0DAmEtB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;oBAUtC,cAAc,yBACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC;gDACT,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAChC,wCAAwC;gDACxC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,IAAI;oDAChC,2BAA2B;gDAC7B,OAAO;oDACL,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDAC7C,MAAM,WAAW,gBACd,GAAG,CAAC,CAAC,QAAU,CAAC;4DACf,MAAM,MAAM,IAAI;4DAChB,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,MACzB,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC;wDAExC,CAAC,GACA,MAAM,CAAC,CAAC,QAAU,MAAM,KAAK,CAAC,MAAM,GAAG;oDAC1C,2BAA2B;gDAC7B;4CACF;;;;;;sDAEF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,OAAM;0DAEN,cAAA,6LAAC;oDACC,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,eAAc;oDACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAI,WAAU;0CACZ,wBAAwB,MAAM,GAAG,IAChC,wBAAwB,GAAG,CAAC,CAAC,sBAC3B,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAG,WAAU;0DACX,MAAM,IAAI;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,sBACrB,6LAAC;wDAEC,WAAU;wDACV,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,GAAG,EAAE;;0EAEpC,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,OAAM;;sFAEN,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;;;;;;;;;;;;0EAIrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ;;;;;;kFAEf,6LAAC;wEAAE,WAAU;kFACV,IAAI,QAAQ,EAAE,iBACb,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,CAAC;;;;;;;;;;;;0EAG1D,6LAAC;gEAAO,WAAU;0EAChB,cAAA,6LAAC;oEACC,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,OAAM;;sFAEN,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;sFAEjB,6LAAC;4EACC,GAAE;4EACF,QAAO;4EACP,aAAY;4EACZ,eAAc;4EACd,gBAAe;;;;;;;;;;;;;;;;;;uDArFhB,GAAG,IAAI,MAAM,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;uCAP3B,MAAM,IAAI;;;;8DAsGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;oBAQ9C,cAAc,yBACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC;gDACT,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDAChC,oCAAoC;gDACpC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,OAAO,IAAI;oDAChC,uBAAuB;gDACzB,OAAO;oDACL,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW;oDAC7C,MAAM,WAAW,YACd,GAAG,CAAC,CAAC,QAAU,CAAC;4DACf,MAAM,MAAM,IAAI;4DAChB,OAAO,MAAM,KAAK,CAAC,MAAM,CACvB,CAAC,OACC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC;wDAEtC,CAAC,GACA,MAAM,CAAC,CAAC,QAAU,MAAM,KAAK,CAAC,MAAM,GAAG;oDAC1C,uBAAuB;gDACzB;4CACF;;;;;;sDAEF,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,OAAM;0DAEN,cAAA,6LAAC;oDACC,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,eAAc;oDACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQzB,6LAAC;gCAAI,WAAU;0CACZ,oBAAoB,MAAM,GAAG,IAC5B,oBAAoB,GAAG,CAAC,CAAC,sBACvB,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAG,WAAU;0DACX,MAAM,IAAI;;;;;;0DAEb,6LAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM;oDACtB,0BAA0B;oDAC1B,MAAM,SAAS,KAAK,GAAG,CACpB,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;oDAEhB,uBAAuB;oDACvB,MAAM,OAAO,IAAI,KAAK,KAAK,SAAS;oDACpC,MAAM,gBAAgB,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;oDAE1H,qBACE,6LAAC;wDAEC,WAAU;wDACV,SAAS,IACP,OAAO,IAAI,CACT,KAAK,GAAG,EACR,UACA;;0EAIJ,6LAAC;gEAAI,WAAU;0EACZ,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;;;;;;0EAEf,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFACV,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EACV,QACA,KAAK,KAAK,CAAC,MAAM,GAAG,KAChB,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,QAC9B,KAAK,KAAK;;;;;;kFAGlB,6LAAC;wEAAE,WAAU;kFACV;;;;;;;;;;;;0EAGL,6LAAC;gEAAI,WAAU;0EACZ;;;;;;;uDA3BE;;;;;gDA+BX;;;;;;;uCAhDM,MAAM,IAAI;;;;8DAqDtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;gDACL,OAAM;0DAEN,cAAA,6LAAC;oDACC,GAAE;oDACF,QAAO;oDACP,aAAY;oDACZ,eAAc;oDACd,gBAAe;;;;;;;;;;;;;;;;sDAIrB,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQhD,mBAAmB,WAAW,MAAM,GAAG,mBACtC,6LAAC,6IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS;oBACP,mBAAmB;gBACrB;gBACA,OAAO,WAAW,GAAG,CAAC;gBACtB,cAAc;;;;;;;;;;;;AAKxB;GAtmBwB;KAAA", "debugId": null}}, {"offset": {"line": 7407, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/testnext/bond-hub-fe/src/components/chat/ConverstationInfo.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef } from \"react\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { User, UserInfo, Media, GroupRole } from \"@/types/base\";\r\nimport { getLinkIcon, getLinkTitle } from \"@/utils/link-utils\";\r\nimport MediaViewer from \"@/components/media/MediaViewer\";\r\nimport {\r\n  X,\r\n  Bell,\r\n  Pin,\r\n  FileImage,\r\n  ChevronRight,\r\n  Trash,\r\n  Video,\r\n  ChevronDown,\r\n  Users,\r\n} from \"lucide-react\";\r\nimport MediaGalleryView from \"./MediaGalleryView\";\r\n// Removed ScrollArea import to fix infinite update loop\r\nimport ProfileDialog from \"@/components/profile/ProfileDialog\";\r\nimport CreateGroupDialog from \"@/components/group/CreateGroupDialog\";\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport { useChatStore } from \"@/stores/chatStore\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\nimport { toast } from \"sonner\";\r\n\r\ninterface ContactInfoProps {\r\n  contact?:\r\n    | (User & { userInfo: UserInfo; online?: boolean; lastSeen?: Date })\r\n    | null;\r\n  group?: {\r\n    id: string;\r\n    name: string;\r\n    avatarUrl?: string | null;\r\n    createdAt?: Date;\r\n    memberIds?: string[];\r\n    memberUsers?: Array<{\r\n      id: string;\r\n      fullName: string;\r\n      profilePictureUrl?: string | null;\r\n      role: GroupRole;\r\n    }>;\r\n  } | null;\r\n  onClose: () => void;\r\n  isOverlay?: boolean;\r\n}\r\n\r\nexport default function ContactInfo({\r\n  contact,\r\n  onClose,\r\n  isOverlay = false,\r\n}: ContactInfoProps) {\r\n  const [mediaFiles, setMediaFiles] = useState<(Media & { createdAt: Date })[]>(\r\n    [],\r\n  );\r\n  const [documents, setDocuments] = useState<(Media & { createdAt: Date })[]>(\r\n    [],\r\n  );\r\n  const [showMediaGallery, setShowMediaGallery] = useState(false);\r\n  const [showMediaViewer, setShowMediaViewer] = useState(false);\r\n  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);\r\n  const [links, setLinks] = useState<\r\n    { url: string; title: string; timestamp: Date }[]\r\n  >([]);\r\n  const [isLoadingMedia, setIsLoadingMedia] = useState(true);\r\n  const [showProfileDialog, setShowProfileDialog] = useState(false);\r\n  const [activeGalleryTab, setActiveGalleryTab] = useState<\r\n    \"media\" | \"files\" | \"links\"\r\n  >(\"media\");\r\n  const [showCreateGroupDialog, setShowCreateGroupDialog] = useState(false);\r\n\r\n  // Sử dụng ref để lưu trữ ID liên hệ trước đó - phải đặt ở ngoài useEffect\r\n  const prevContactIdRef = useRef<string | null>(null);\r\n\r\n  // Reset media gallery, viewer, and create group dialog when contact changes\r\n  // Tối ưu hóa để tránh vòng lặp vô hạn\r\n  useEffect(() => {\r\n    if (!contact?.id) return;\r\n\r\n    // Chỉ reset khi ID liên hệ thay đổi\r\n    if (prevContactIdRef.current !== contact.id) {\r\n      console.log(\r\n        `[ContactInfo] Contact changed from ${prevContactIdRef.current} to ${contact.id}, resetting state`,\r\n      );\r\n\r\n      // Cập nhật ID liên hệ hiện tại\r\n      prevContactIdRef.current = contact.id;\r\n\r\n      // Reset các state\r\n      setShowMediaGallery(false);\r\n      setShowMediaViewer(false);\r\n      setShowCreateGroupDialog(false);\r\n    }\r\n  }, [contact?.id]);\r\n\r\n  const messages = useChatStore((state) => state.messages);\r\n  const currentUser = useAuthStore((state) => state.user);\r\n\r\n  // Lấy media từ tin nhắn - tối ưu hóa để tránh vòng lặp vô hạn\r\n  useEffect(() => {\r\n    if (!contact?.id) return;\r\n\r\n    // Lưu ID liên hệ hiện tại vào biến local\r\n    const currentContactId = contact.id;\r\n\r\n    // Tạo một ID duy nhất cho lần chạy này của useEffect\r\n    const effectId = Math.random().toString(36).substring(2, 9);\r\n\r\n    console.log(\r\n      `[ContactInfo:${effectId}] Starting media extraction for contact ${currentContactId}`,\r\n    );\r\n\r\n    // Sử dụng setTimeout để tránh nhiều lần render liên tiếp\r\n    const timeoutId = setTimeout(() => {\r\n      setIsLoadingMedia(true);\r\n\r\n      // Lọc media từ tin nhắn hiện có\r\n      const extractMediaFromMessages = () => {\r\n        const imageAndVideoFiles: (Media & {\r\n          createdAt: Date;\r\n          sender?: unknown;\r\n          senderId?: string;\r\n        })[] = [];\r\n        const documentFiles: (Media & {\r\n          createdAt: Date;\r\n          sender?: unknown;\r\n          senderId?: string;\r\n        })[] = [];\r\n        const extractedLinks: {\r\n          url: string;\r\n          title: string;\r\n          timestamp: Date;\r\n        }[] = [];\r\n\r\n        messages.forEach((message) => {\r\n          // Bỏ qua các tin nhắn đã thu hồi\r\n          if (message.recalled) return;\r\n\r\n          // Xử lý media\r\n          if (message.content.media && message.content.media.length > 0) {\r\n            message.content.media.forEach((media) => {\r\n              if (!media.metadata || !media.metadata.extension) return;\r\n              const extension = media.metadata.extension.toLowerCase();\r\n              if (\r\n                [\r\n                  \"jpg\",\r\n                  \"jpeg\",\r\n                  \"png\",\r\n                  \"gif\",\r\n                  \"webp\",\r\n                  \"mp4\",\r\n                  \"webm\",\r\n                  \"mov\",\r\n                ].includes(extension)\r\n              ) {\r\n                imageAndVideoFiles.push({\r\n                  ...media,\r\n                  createdAt: new Date(message.createdAt),\r\n                  senderId: message.senderId,\r\n                  sender: message.sender,\r\n                });\r\n              } else {\r\n                documentFiles.push({\r\n                  ...media,\r\n                  createdAt: new Date(message.createdAt),\r\n                  senderId: message.senderId,\r\n                  sender: message.sender,\r\n                });\r\n              }\r\n            });\r\n          }\r\n\r\n          // Xử lý links trong text\r\n          if (message.content.text) {\r\n            const urlRegex = /(https?:\\/\\/[^\\s]+)/g;\r\n            const matches = message.content.text.match(urlRegex);\r\n            if (matches) {\r\n              matches.forEach((url) => {\r\n                // Get domain for display\r\n                const domain = url.replace(/^https?:\\/\\//, \"\").split(\"/\")[0];\r\n\r\n                // Use the utility function to get a better title\r\n                const title = getLinkTitle(domain, url);\r\n\r\n                extractedLinks.push({\r\n                  url,\r\n                  title,\r\n                  timestamp: new Date(message.createdAt),\r\n                });\r\n              });\r\n            }\r\n          }\r\n        });\r\n\r\n        // Sắp xếp media từ mới đến cũ\r\n        imageAndVideoFiles.sort(\r\n          (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n        );\r\n        documentFiles.sort(\r\n          (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),\r\n        );\r\n        extractedLinks.sort(\r\n          (a, b) => b.timestamp.getTime() - a.timestamp.getTime(),\r\n        );\r\n\r\n        setMediaFiles(imageAndVideoFiles.slice(0, 20)); // Giới hạn 20 file\r\n        setDocuments(documentFiles.slice(0, 10)); // Giới hạn 10 file\r\n        setLinks(extractedLinks.slice(0, 10)); // Giới hạn 10 link\r\n        setIsLoadingMedia(false);\r\n      };\r\n\r\n      extractMediaFromMessages();\r\n    }, 100); // Thêm độ trễ nhỏ để tránh nhiều lần render liên tiếp\r\n\r\n    // Cleanup function để tránh memory leak và cập nhật state sau khi component unmount\r\n    return () => {\r\n      clearTimeout(timeoutId);\r\n      console.log(\r\n        `[ContactInfo:${effectId}] Cleanup for contact ${currentContactId}`,\r\n      );\r\n    };\r\n  }, [contact?.id]); // Loại bỏ messages từ dependencies để tránh vòng lặp vô hạn\r\n\r\n  if (!contact) {\r\n    return null;\r\n  }\r\n\r\n  if (showMediaGallery) {\r\n    return (\r\n      <MediaGalleryView\r\n        mediaFiles={mediaFiles}\r\n        documents={documents}\r\n        links={links}\r\n        initialTab={activeGalleryTab}\r\n        onClose={() => setShowMediaGallery(false)}\r\n      />\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={`h-full flex flex-col bg-white ${!isOverlay ? \"border-l\" : \"\"}`}\r\n    >\r\n      <div className=\"p-4 flex items-center justify-between border-b\">\r\n        <h2 className=\"font-semibold\">Thông tin hội thoại</h2>\r\n        <Button\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className={`${isOverlay ? \"bg-gray-100 hover:bg-gray-200\" : \"rounded-full\"}`}\r\n          onClick={onClose}\r\n        >\r\n          <X className=\"h-5 w-5\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-y-auto\">\r\n        <div className=\"space-y-2 bg-[#ebecf0]\">\r\n          {/* Thông tin người dùng */}\r\n          <div className=\"flex flex-col items-center text-center bg-white p-2\">\r\n            <Avatar\r\n              className=\"h-20 w-20 mb-3 cursor-pointer\"\r\n              onClick={() => setShowProfileDialog(true)}\r\n            >\r\n              <AvatarImage\r\n                src={contact.userInfo?.profilePictureUrl || undefined}\r\n                className=\"object-cover\"\r\n              />\r\n              <AvatarFallback className=\"text-xl\">\r\n                {contact.userInfo?.fullName?.slice(0, 2).toUpperCase() || \"??\"}\r\n              </AvatarFallback>\r\n            </Avatar>\r\n            <div className=\"flex items-center justify-center gap-2\">\r\n              <h2 className=\"text-lg font-semibold\">\r\n                {contact.userInfo?.fullName}\r\n              </h2>\r\n            </div>\r\n\r\n            {/* Các chức năng chính */}\r\n            <div className=\"grid grid-cols-3 gap-4 w-full m-2\">\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\r\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n                >\r\n                  <Bell className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Tắt thông báo</span>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1 opacity-60\"\r\n                  onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n                >\r\n                  <Pin className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Ghim hội thoại</span>\r\n              </div>\r\n\r\n              <div className=\"flex flex-col items-center\">\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"icon\"\r\n                  className=\"h-10 w-10 rounded-full bg-blue-50 text-blue-500 mb-1\"\r\n                  onClick={() => setShowCreateGroupDialog(true)}\r\n                >\r\n                  <Users className=\"h-6 w-6\" />\r\n                </Button>\r\n                <span className=\"text-xs\">Tạo nhóm chat</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Ảnh/Video */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Ảnh/Video</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : mediaFiles.length > 0 ? (\r\n                <div className=\"p-3\">\r\n                  <div className=\"grid grid-cols-4 gap-1\">\r\n                    {mediaFiles.slice(0, 8).map((media, index) => (\r\n                      <div\r\n                        key={index}\r\n                        className=\"aspect-square relative overflow-hidden border border-gray-200 rounded-md cursor-pointer\"\r\n                        onClick={() => {\r\n                          setSelectedMediaIndex(index);\r\n                          setShowMediaViewer(true);\r\n                        }}\r\n                      >\r\n                        {media.metadata?.extension?.match(/mp4|webm|mov/i) ||\r\n                        media.type === \"VIDEO\" ? (\r\n                          <>\r\n                            <div className=\"w-full h-full bg-black\">\r\n                              <video\r\n                                src={media.url}\r\n                                className=\"object-cover w-full h-full\"\r\n                                preload=\"metadata\"\r\n                                muted\r\n                              />\r\n                            </div>\r\n                            <div className=\"absolute inset-0 flex items-center justify-center bg-black/30\">\r\n                              <Video className=\"h-6 w-6 text-white\" />\r\n                            </div>\r\n                          </>\r\n                        ) : (\r\n                          <div\r\n                            className=\"w-full h-full bg-cover bg-center\"\r\n                            style={{ backgroundImage: `url(${media.url})` }}\r\n                          ></div>\r\n                        )}\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                  <div className=\"mt-2\">\r\n                    <Button\r\n                      variant=\"ghost\"\r\n                      size=\"sm\"\r\n                      className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                      onClick={() => {\r\n                        setActiveGalleryTab(\"media\");\r\n                        setShowMediaGallery(true);\r\n                      }}\r\n                    >\r\n                      Xem tất cả\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">\r\n                    Không có ảnh hoặc video nào\r\n                  </p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* File */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">File</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : documents.length > 0 ? (\r\n                <div className=\"space-y-2 pb-2\">\r\n                  {documents.slice(0, 3).map((doc, index) => (\r\n                    <div\r\n                      key={index}\r\n                      className=\"flex items-center py-2 px-4 hover:bg-gray-200 cursor-pointer\"\r\n                      onClick={() => window.open(doc.url, \"_blank\")}\r\n                    >\r\n                      <div className=\"bg-blue-100 p-2 rounded-md mr-3\">\r\n                        <FileImage className=\"h-5 w-5 text-blue-500\" />\r\n                      </div>\r\n                      <div className=\"flex-1 min-w-0\">\r\n                        <p className=\"font-medium text-sm truncate\">\r\n                          {doc.fileName}\r\n                        </p>\r\n                        <p className=\"text-xs text-gray-500\">\r\n                          {doc.metadata?.sizeFormatted ||\r\n                            `${Math.round((doc.metadata?.size || 0) / 1024)} KB`}\r\n                        </p>\r\n                      </div>\r\n                      <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8\">\r\n                        <ChevronRight className=\"h-4 w-4\" />\r\n                      </Button>\r\n                    </div>\r\n                  ))}\r\n                  {documents.length > 3 && (\r\n                    <div className=\"mt-2 px-2 text-center\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                        onClick={() => {\r\n                          setActiveGalleryTab(\"files\");\r\n                          setShowMediaGallery(true);\r\n                        }}\r\n                      >\r\n                        Xem tất cả\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">Không có file nào</p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Link */}\r\n          <Collapsible defaultOpen className=\"overflow-hidden bg-white\">\r\n            <CollapsibleTrigger className=\"flex items-center justify-between w-full p-3 hover:bg-gray-50\">\r\n              <div className=\"flex items-center\">\r\n                <span className=\"font-semibold\">Link</span>\r\n              </div>\r\n              <ChevronDown className=\"h-5 w-5 text-gray-500\" />\r\n            </CollapsibleTrigger>\r\n            <CollapsibleContent>\r\n              {isLoadingMedia ? (\r\n                <div className=\"p-4 text-center\">\r\n                  <div className=\"animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full mx-auto\"></div>\r\n                  <p className=\"text-sm text-gray-500 mt-2\">Đang tải...</p>\r\n                </div>\r\n              ) : links.length > 0 ? (\r\n                <div className=\"space-y-2 pb-2\">\r\n                  {links.slice(0, 3).map((link, index) => {\r\n                    // Extract domain from URL\r\n                    const domain = link.url\r\n                      .replace(/^https?:\\/\\//, \"\")\r\n                      .split(\"/\")[0];\r\n                    // Format date as DD/MM\r\n                    const date = new Date(link.timestamp);\r\n                    const formattedDate = `${date.getDate().toString().padStart(2, \"0\")}/${(date.getMonth() + 1).toString().padStart(2, \"0\")}`;\r\n\r\n                    return (\r\n                      <div\r\n                        key={index}\r\n                        className=\"flex items-center py-2 px-4 hover:bg-gray-200 cursor-pointer\"\r\n                        onClick={() =>\r\n                          window.open(link.url, \"_blank\", \"noopener,noreferrer\")\r\n                        }\r\n                      >\r\n                        <div className=\"w-10 h-10 rounded-md mr-3 flex items-center justify-center overflow-hidden\">\r\n                          {getLinkIcon(domain)}\r\n                        </div>\r\n                        <div className=\"flex-1 min-w-0 mr-2\">\r\n                          <p className=\"font-medium text-sm truncate max-w-[180px]\">\r\n                            {getLinkTitle(\r\n                              domain,\r\n                              link.title.length > 40\r\n                                ? link.title.substring(0, 40) + \"...\"\r\n                                : link.title,\r\n                            )}\r\n                          </p>\r\n                          <p className=\"text-xs text-blue-500 truncate\">\r\n                            {domain}\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"text-xs text-gray-500 whitespace-nowrap\">\r\n                          {formattedDate}\r\n                        </div>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                  {links.length > 3 && (\r\n                    <div className=\"mt-2 px-2 text-center\">\r\n                      <Button\r\n                        variant=\"ghost\"\r\n                        size=\"sm\"\r\n                        className=\"text-sm font-semibold w-full bg-[#e5e7eb] hover:bg-gray-300\"\r\n                        onClick={() => {\r\n                          setActiveGalleryTab(\"links\");\r\n                          setShowMediaGallery(true);\r\n                        }}\r\n                      >\r\n                        Xem tất cả\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ) : (\r\n                <div className=\"p-4 text-center\">\r\n                  <p className=\"text-sm text-gray-500\">Không có link nào</p>\r\n                </div>\r\n              )}\r\n            </CollapsibleContent>\r\n          </Collapsible>\r\n\r\n          {/* Cài đặt hội thoại */}\r\n          <div className=\"space-y-1 bg-white p-2\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              className=\"w-full justify-start text-red-500 pl-2\"\r\n              onClick={() => toast.info(\"Tính năng này chưa được hỗ trợ\")}\r\n            >\r\n              <Trash className=\"h-5 w-5 mr-3\" />\r\n              <span>Xóa cuộc trò chuyện</span>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {showProfileDialog && contact && (\r\n        <ProfileDialog\r\n          user={contact}\r\n          isOpen={showProfileDialog}\r\n          onOpenChange={setShowProfileDialog}\r\n          isOwnProfile={contact.id === currentUser?.id}\r\n        />\r\n      )}\r\n\r\n      {/* Create Group Dialog */}\r\n      {contact && (\r\n        <CreateGroupDialog\r\n          isOpen={showCreateGroupDialog}\r\n          onOpenChange={setShowCreateGroupDialog}\r\n          preSelectedFriendId={contact.id}\r\n        />\r\n      )}\r\n\r\n      {/* Media Viewer */}\r\n      {showMediaViewer && mediaFiles.length > 0 && (\r\n        <MediaViewer\r\n          isOpen={showMediaViewer}\r\n          onClose={() => setShowMediaViewer(false)}\r\n          media={mediaFiles.map((media) => ({\r\n            ...media,\r\n            // Ensure type is set correctly for videos\r\n            type:\r\n              media.metadata?.extension?.match(/mp4|webm|mov/i) ||\r\n              media.type === \"VIDEO\"\r\n                ? \"VIDEO\"\r\n                : \"IMAGE\",\r\n          }))}\r\n          initialIndex={selectedMediaIndex}\r\n          chatName={contact.userInfo?.fullName || \"Hội thoại\"}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA,wDAAwD;AACxD;AACA;AACA;AAKA;AACA;AACA;;;AA9BA;;;;;;;;;;;;;;AAqDe,SAAS,YAAY,EAClC,OAAO,EACP,OAAO,EACP,YAAY,KAAK,EACA;;IACjB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACzC,EAAE;IAEJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvC,EAAE;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/B,EAAE;IACJ,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErD;IACF,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,0EAA0E;IAC1E,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAE/C,4EAA4E;IAC5E,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,SAAS,IAAI;YAElB,oCAAoC;YACpC,IAAI,iBAAiB,OAAO,KAAK,QAAQ,EAAE,EAAE;gBAC3C,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,iBAAiB,OAAO,CAAC,IAAI,EAAE,QAAQ,EAAE,CAAC,iBAAiB,CAAC;gBAGpG,+BAA+B;gBAC/B,iBAAiB,OAAO,GAAG,QAAQ,EAAE;gBAErC,kBAAkB;gBAClB,oBAAoB;gBACpB,mBAAmB;gBACnB,yBAAyB;YAC3B;QACF;gCAAG;QAAC,SAAS;KAAG;IAEhB,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;8CAAE,CAAC,QAAU,MAAM,QAAQ;;IACvD,MAAM,cAAc,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;iDAAE,CAAC,QAAU,MAAM,IAAI;;IAEtD,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,SAAS,IAAI;YAElB,yCAAyC;YACzC,MAAM,mBAAmB,QAAQ,EAAE;YAEnC,qDAAqD;YACrD,MAAM,WAAW,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAEzD,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,SAAS,wCAAwC,EAAE,kBAAkB;YAGvF,yDAAyD;YACzD,MAAM,YAAY;mDAAW;oBAC3B,kBAAkB;oBAElB,gCAAgC;oBAChC,MAAM;oFAA2B;4BAC/B,MAAM,qBAIC,EAAE;4BACT,MAAM,gBAIC,EAAE;4BACT,MAAM,iBAIA,EAAE;4BAER,SAAS,OAAO;4FAAC,CAAC;oCAChB,iCAAiC;oCACjC,IAAI,QAAQ,QAAQ,EAAE;oCAEtB,cAAc;oCACd,IAAI,QAAQ,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG;wCAC7D,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO;wGAAC,CAAC;gDAC7B,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,QAAQ,CAAC,SAAS,EAAE;gDAClD,MAAM,YAAY,MAAM,QAAQ,CAAC,SAAS,CAAC,WAAW;gDACtD,IACE;oDACE;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,QAAQ,CAAC,YACX;oDACA,mBAAmB,IAAI,CAAC;wDACtB,GAAG,KAAK;wDACR,WAAW,IAAI,KAAK,QAAQ,SAAS;wDACrC,UAAU,QAAQ,QAAQ;wDAC1B,QAAQ,QAAQ,MAAM;oDACxB;gDACF,OAAO;oDACL,cAAc,IAAI,CAAC;wDACjB,GAAG,KAAK;wDACR,WAAW,IAAI,KAAK,QAAQ,SAAS;wDACrC,UAAU,QAAQ,QAAQ;wDAC1B,QAAQ,QAAQ,MAAM;oDACxB;gDACF;4CACF;;oCACF;oCAEA,yBAAyB;oCACzB,IAAI,QAAQ,OAAO,CAAC,IAAI,EAAE;wCACxB,MAAM,WAAW;wCACjB,MAAM,UAAU,QAAQ,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;wCAC3C,IAAI,SAAS;4CACX,QAAQ,OAAO;4GAAC,CAAC;oDACf,yBAAyB;oDACzB,MAAM,SAAS,IAAI,OAAO,CAAC,gBAAgB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;oDAE5D,iDAAiD;oDACjD,MAAM,QAAQ,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EAAE,QAAQ;oDAEnC,eAAe,IAAI,CAAC;wDAClB;wDACA;wDACA,WAAW,IAAI,KAAK,QAAQ,SAAS;oDACvC;gDACF;;wCACF;oCACF;gCACF;;4BAEA,8BAA8B;4BAC9B,mBAAmB,IAAI;4FACrB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAEvD,cAAc,IAAI;4FAChB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAEvD,eAAe,IAAI;4FACjB,CAAC,GAAG,IAAM,EAAE,SAAS,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC,OAAO;;4BAGvD,cAAc,mBAAmB,KAAK,CAAC,GAAG,MAAM,mBAAmB;4BACnE,aAAa,cAAc,KAAK,CAAC,GAAG,MAAM,mBAAmB;4BAC7D,SAAS,eAAe,KAAK,CAAC,GAAG,MAAM,mBAAmB;4BAC1D,kBAAkB;wBACpB;;oBAEA;gBACF;kDAAG,MAAM,sDAAsD;YAE/D,oFAAoF;YACpF;yCAAO;oBACL,aAAa;oBACb,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,SAAS,sBAAsB,EAAE,kBAAkB;gBAEvE;;QACF;gCAAG;QAAC,SAAS;KAAG,GAAG,4DAA4D;IAE/E,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,IAAI,kBAAkB;QACpB,qBACE,6LAAC,iJAAA,CAAA,UAAgB;YACf,YAAY;YACZ,WAAW;YACX,OAAO;YACP,YAAY;YACZ,SAAS,IAAM,oBAAoB;;;;;;IAGzC;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,8BAA8B,EAAE,CAAC,YAAY,aAAa,IAAI;;0BAE1E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,GAAG,YAAY,kCAAkC,gBAAgB;wBAC5E,SAAS;kCAET,cAAA,6LAAC,+LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAS,IAAM,qBAAqB;;sDAEpC,6LAAC,qIAAA,CAAA,cAAW;4CACV,KAAK,QAAQ,QAAQ,EAAE,qBAAqB;4CAC5C,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,QAAQ,QAAQ,EAAE,UAAU,MAAM,GAAG,GAAG,iBAAiB;;;;;;;;;;;;8CAG9D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDACX,QAAQ,QAAQ,EAAE;;;;;;;;;;;8CAKvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAElB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;8DAE1B,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;8DAEjB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;sDAG5B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,IAAM,yBAAyB;8DAExC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,WAAW,MAAM,GAAG,kBACtB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACZ,WAAW,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;wDAEC,WAAU;wDACV,SAAS;4DACP,sBAAsB;4DACtB,mBAAmB;wDACrB;kEAEC,MAAM,QAAQ,EAAE,WAAW,MAAM,oBAClC,MAAM,IAAI,KAAK,wBACb;;8EACE,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEACC,KAAK,MAAM,GAAG;wEACd,WAAU;wEACV,SAAQ;wEACR,KAAK;;;;;;;;;;;8EAGT,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;yFAIrB,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;4DAAC;;;;;;uDAzB7C;;;;;;;;;;0DA+BX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAML,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAS7C,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,UAAU,MAAM,GAAG,kBACrB,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;oDAEC,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC,IAAI,GAAG,EAAE;;sEAEpC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,mNAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,IAAI,QAAQ;;;;;;8EAEf,6LAAC;oEAAE,WAAU;8EACV,IAAI,QAAQ,EAAE,iBACb,GAAG,KAAK,KAAK,CAAC,CAAC,IAAI,QAAQ,EAAE,QAAQ,CAAC,IAAI,MAAM,GAAG,CAAC;;;;;;;;;;;;sEAG1D,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;4DAAO,WAAU;sEAC5C,cAAA,6LAAC,yNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;mDAjBrB;;;;;4CAqBR,UAAU,MAAM,GAAG,mBAClB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC,0IAAA,CAAA,cAAW;4BAAC,WAAW;4BAAC,WAAU;;8CACjC,6LAAC,0IAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;sDAElC,6LAAC,uNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,0IAAA,CAAA,qBAAkB;8CAChB,+BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;+CAE1C,MAAM,MAAM,GAAG,kBACjB,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM;gDAC5B,0BAA0B;gDAC1B,MAAM,SAAS,KAAK,GAAG,CACpB,OAAO,CAAC,gBAAgB,IACxB,KAAK,CAAC,IAAI,CAAC,EAAE;gDAChB,uBAAuB;gDACvB,MAAM,OAAO,IAAI,KAAK,KAAK,SAAS;gDACpC,MAAM,gBAAgB,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;gDAE1H,qBACE,6LAAC;oDAEC,WAAU;oDACV,SAAS,IACP,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,UAAU;;sEAGlC,6LAAC;4DAAI,WAAU;sEACZ,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;;;;;;sEAEf,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD,EACV,QACA,KAAK,KAAK,CAAC,MAAM,GAAG,KAChB,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,MAAM,QAC9B,KAAK,KAAK;;;;;;8EAGlB,6LAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;;sEAGL,6LAAC;4DAAI,WAAU;sEACZ;;;;;;;mDAvBE;;;;;4CA2BX;4CACC,MAAM,MAAM,GAAG,mBACd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;wDACP,oBAAoB;wDACpB,oBAAoB;oDACtB;8DACD;;;;;;;;;;;;;;;;6DAOP,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAO7C,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC;;kDAE1B,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAMb,qBAAqB,yBACpB,6LAAC,iJAAA,CAAA,UAAa;gBACZ,MAAM;gBACN,QAAQ;gBACR,cAAc;gBACd,cAAc,QAAQ,EAAE,KAAK,aAAa;;;;;;YAK7C,yBACC,6LAAC,mJAAA,CAAA,UAAiB;gBAChB,QAAQ;gBACR,cAAc;gBACd,qBAAqB,QAAQ,EAAE;;;;;;YAKlC,mBAAmB,WAAW,MAAM,GAAG,mBACtC,6LAAC,6IAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,OAAO,WAAW,GAAG,CAAC,CAAC,QAAU,CAAC;wBAChC,GAAG,KAAK;wBACR,0CAA0C;wBAC1C,MACE,MAAM,QAAQ,EAAE,WAAW,MAAM,oBACjC,MAAM,IAAI,KAAK,UACX,UACA;oBACR,CAAC;gBACD,cAAc;gBACd,UAAU,QAAQ,QAAQ,EAAE,YAAY;;;;;;;;;;;;AAKlD;GA1hBwB;;QAgDL,6HAAA,CAAA,eAAY;QACT,6HAAA,CAAA,eAAY;;;KAjDV", "debugId": null}}]}